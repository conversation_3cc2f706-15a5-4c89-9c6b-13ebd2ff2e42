package kr.co.passorder.passordersearchservice.global.error

interface CustomErrorObject {
    val httpStatusCode: Int
    val name: String
    val message: String

    fun withDetailMessage(detailMessage: String?): CustomErrorObject {
        if (detailMessage == null) {
            return this
        }

        val newHttpStatusCode = httpStatusCode
        val newName = name
        val newMessage = "$message [$detailMessage]"
        return object : CustomErrorObject {
            override val httpStatusCode: Int = newHttpStatusCode
            override val name: String = newName
            override val message: String = newMessage
        }
    }
}
