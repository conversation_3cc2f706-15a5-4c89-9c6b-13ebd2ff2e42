package kr.co.passorder.passordersearchservice.global.error

import org.springframework.http.HttpStatus

enum class ErrorCode(
    override val httpStatusCode: HttpStatus,
    override val message: String
) : ErrorObject {
    SERVER_ERROR(HttpStatus.INTERNAL_SERVER_ERROR, "Server Error"),
    METHOD_NOT_ALLOWED(HttpStatus.METHOD_NOT_ALLOWED, "Not Allowed HTTP Method"),
    INVALID_POST(HttpStatus.BAD_REQUEST, "Invalid Request Body"),
    INVALID_REQUEST_HEADER(HttpStatus.BAD_REQUEST, "Invalid Request Header"),
    INVALID_REQUEST_PARAMETER(HttpStatus.BAD_REQUEST, "Invalid Request Parameter"),
    INVALID_REQUEST_DATA(HttpStatus.BAD_REQUEST, "Invalid Request Data"),
    NOT_IMPLEMENTED(HttpStatus.NOT_IMPLEMENTED, "Service Implementing"),
    MISSING_REQUEST_PARAMETER_VALUE(HttpStatus.BAD_REQUEST, "Missing Request Parameter Data"),
    USER_ID_HEADER_NOT_FOUND(HttpStatus.BAD_REQUEST, "User header not found.")
}
