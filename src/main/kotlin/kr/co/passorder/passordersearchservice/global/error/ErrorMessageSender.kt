package kr.co.passorder.passordersearchservice.global.error

import kr.co.passorder.passordersearchservice.global.contants.ZoneOffsetConstant
import kr.co.passorder.passordersearchservice.global.property.SlackMessageProperty
import kr.co.passorder.passordersearchservice.message.adapter.out.web.slack.SlackMessageClient
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class ErrorMessageSender(
    private val slackMessageProperty: SlackMessageProperty,
    private val slackMessageClient: SlackMessageClient
) {

    fun sendExceptionMessage(e: Exception, prefixMessage: String? = null) {
        slackMessageClient.sendMessage(
            authorization = "Bearer ${slackMessageProperty.token}",
            channel = slackMessageProperty.backEndErrorChannel,
            text = message((prefixMessage ?: "") + e.message),
        )
    }

    fun sendExceptionMessage(
        channel: String,
        message: String? = null
    ) {
        slackMessageClient.sendMessage(
            authorization = "Bearer ${slackMessageProperty.token}",
            channel = slackMessageProperty.backEndErrorChannel,
            text = message((message ?: "")),
        )
    }

    private fun message(message: String?) = """
        ${slackMessageProperty.backEndErrorTitle}
        
        ```
        message = [${message ?: "message not exist"}]
        
        date = [${OffsetDateTime.now().withOffsetSameInstant(ZoneOffsetConstant.ASIA_SEOUL)}]
        ```
    """.trimIndent()
}
