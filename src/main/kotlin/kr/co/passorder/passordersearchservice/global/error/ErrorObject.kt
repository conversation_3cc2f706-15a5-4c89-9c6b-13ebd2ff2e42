package kr.co.passorder.passordersearchservice.global.error

import org.springframework.http.HttpStatus

interface ErrorObject {
    val httpStatusCode: HttpStatus
    val name: String
    val message: String

    fun withDetailMessage(detailMessage: String?): ErrorObject {
        if (detailMessage == null) {
            return this
        }

        val newHttpStatusCode = httpStatusCode
        val newName = name
        val newMessage = "$message [$detailMessage]"
        return object : ErrorObject {
            override val httpStatusCode: HttpStatus = newHttpStatusCode
            override val name: String = newName
            override val message: String = newMessage
        }
    }
}
