package kr.co.passorder.passordersearchservice.global.error

data class ErrorResponse(
    val code: String,
    val message: String?
) {

    constructor(errorCode: ErrorCode) : this(
        code = errorCode.name,
        message = errorCode.message
    )

    constructor(e: BusinessException) : this(
        code = e.errorObject.name,
        message = e.message
    )

    constructor(e: BusinessCustomException) : this(
        code = e.errorObject.name,
        message = e.message
    )
}
