package kr.co.passorder.passordersearchservice.global.error

import kr.co.passorder.passordersearchservice.global.error.ErrorCode.INVALID_REQUEST_PARAMETER
import kr.co.passorder.passordersearchservice.global.extension.KLoggerExtension.crit
import mu.KotlinLogging
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.validation.BindException
import org.springframework.web.HttpRequestMethodNotSupportedException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.MissingRequestHeaderException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.client.HttpServerErrorException
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException
import javax.validation.ConstraintViolationException

@RestControllerAdvice
class GlobalExceptionHandler(val errorMessageSender: ErrorMessageSender) {

    private val logger = KotlinLogging.logger {}

    /**
     * (@RequestBody) Validation 유효성 예외시 발생
     */
    @ExceptionHandler(value = [MethodArgumentNotValidException::class])
    protected fun handleMethodArgumentNotValidException(
        e: MethodArgumentNotValidException,
    ): ResponseEntity<ErrorResponse> {
        logger.warn("handleMethodArgumentNotValidException", e)

        val response = ErrorResponse(ErrorCode.INVALID_REQUEST_DATA)
        return ResponseEntity(response, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [ConstraintViolationException::class])
    protected fun handleConstraintViolationException(
        e: ConstraintViolationException,
    ): ResponseEntity<ErrorResponse> {
        logger.warn("handleConstraintViolationException", e)

        val response = ErrorResponse(ErrorCode.INVALID_REQUEST_DATA)
        return ResponseEntity(response, HttpStatus.BAD_REQUEST)
    }

    /**
     * Request Parameter에 필요한 값을 넣지 않은 경우 예외 발생
     */
    @ExceptionHandler(value = [MissingServletRequestParameterException::class])
    protected fun handleMissingServletRequestParameterException(
        e: MissingServletRequestParameterException,
    ): ResponseEntity<ErrorResponse> {
        logger.warn("handleMissingServletRequestParameterException", e)

        val response = ErrorResponse(ErrorCode.MISSING_REQUEST_PARAMETER_VALUE)
        return ResponseEntity(response, HttpStatus.BAD_REQUEST)
    }

    /**
     * @ModelAttribute 으로 binding error 발생시 BindException 발생
     * 또는 Validation 하기 전 Bean Property가 Bind에 실패해도 발생
     */
    @ExceptionHandler(BindException::class)
    protected fun handleBindException(e: BindException): ResponseEntity<ErrorResponse> {
        logger.warn("handleBindException", e)

        val response = ErrorResponse(
            code = INVALID_REQUEST_PARAMETER.name,
            message = "${INVALID_REQUEST_PARAMETER.message} ${e.message}"
        )

        return ResponseEntity(response, HttpStatus.BAD_REQUEST)
    }

    /**
     * RequestParam 의 타입이 맞지 않을 경우 발생
     */
    @ExceptionHandler(value = [MethodArgumentTypeMismatchException::class])
    protected fun handleMethodArgumentTypeMismatchException(
        e: MethodArgumentTypeMismatchException,
    ): ResponseEntity<ErrorResponse> {
        logger.warn("handleMethodArgumentTypeMismatchException", e)

        val response = ErrorResponse(INVALID_REQUEST_PARAMETER)
        return ResponseEntity(response, HttpStatus.BAD_REQUEST)
    }

    /**
     * POST 메소드의 RequestBody 형식이 잘못 되거나 비어있을 경우 발생
     */
    @ExceptionHandler(value = [HttpMessageNotReadableException::class])
    protected fun handleHttpMessageNotReadableException(
        e: HttpMessageNotReadableException,
    ): ResponseEntity<ErrorResponse> {
        logger.warn("handleHttpMessageNotReadableException", e)

        val response = ErrorResponse(ErrorCode.INVALID_POST)
        return ResponseEntity(response, HttpStatus.BAD_REQUEST)
    }

    /**
     * 지원하지 않은 HTTP Method 요청시 발생
     */
    @ExceptionHandler(value = [HttpRequestMethodNotSupportedException::class])
    protected fun handleHttpRequestMethodNotSupportedException(
        e: HttpRequestMethodNotSupportedException,
    ): ResponseEntity<ErrorResponse> {
        logger.warn("handleHttpRequestMethodNotSupportedException", e)

        val response = ErrorResponse(ErrorCode.METHOD_NOT_ALLOWED)
        return ResponseEntity(response, HttpStatus.METHOD_NOT_ALLOWED)
    }

    @ExceptionHandler(value = [BusinessException::class])
    protected fun handleBusinessException(e: BusinessException): ResponseEntity<ErrorResponse> {
        when (e.errorObject.httpStatusCode.value()) {
            in 500..599 -> {
                logger.crit("handleBusinessException", e)
                errorMessageSender.sendExceptionMessage(e)
            }

            else -> {
                logger.warn("handleBusinessException", e)
            }
        }
        return ResponseEntity(ErrorResponse(e), e.errorObject.httpStatusCode)
    }

    @ExceptionHandler(value = [BusinessCustomException::class])
    protected fun handleBusinessCustomException(e: BusinessCustomException): ResponseEntity<ErrorResponse> {
        when (e.errorObject.httpStatusCode) {
            in 500..599 -> {
                logger.error("handleBusinessCustomException", e)
                errorMessageSender.sendExceptionMessage(e)
            }

            else -> {
                logger.warn("handleBusinessCustomException", e)
            }
        }
        return ResponseEntity(ErrorResponse(e), null, e.errorObject.httpStatusCode)
    }

    @ExceptionHandler(value = [HttpServerErrorException::class])
    protected fun handleHttpServerErrorException(e: HttpServerErrorException): ResponseEntity<ErrorResponse> {
        logger.crit("handleHttpServerErrorException", e)

        val response = ErrorResponse(ErrorCode.SERVER_ERROR)
        return ResponseEntity(response, HttpStatus.INTERNAL_SERVER_ERROR)
    }

    /**
     * 서버 에러
     */
    @ExceptionHandler(value = [Exception::class])
    protected fun handleException(e: Exception): ResponseEntity<ErrorResponse> {
        logger.error("handleException: ${e.message}", e)

        val response = ErrorResponse(ErrorCode.SERVER_ERROR)
        return ResponseEntity(response, HttpStatus.INTERNAL_SERVER_ERROR)
    }

    @ExceptionHandler(value = [IllegalStateException::class, IllegalArgumentException::class])
    protected fun handleCheckOrRequiredError(e: Exception): ResponseEntity<ErrorResponse> {
        logger.crit("handleIllegalStateException", e)

        errorMessageSender.sendExceptionMessage(e, "check or required exception 발생. ")

        val response = ErrorResponse(ErrorCode.SERVER_ERROR)
        return ResponseEntity(response, HttpStatus.INTERNAL_SERVER_ERROR)
    }

    @ExceptionHandler(value = [MissingRequestHeaderException::class])
    protected fun handleMissingRequestHeaderException(e: Exception): ResponseEntity<ErrorResponse> {
        logger.crit("handleMissingRequestHeaderException", e)

        val response = ErrorResponse(ErrorCode.INVALID_REQUEST_HEADER)
        return ResponseEntity.badRequest().body(response)
    }
}
