package kr.co.passorder.passordersearchservice.global.feign

import feign.FeignException.errorStatus
import feign.Response
import feign.codec.ErrorDecoder
import kr.co.passorder.passordersearchservice.global.error.BusinessException
import kr.co.passorder.passordersearchservice.global.error.ErrorCode
import mu.KotlinLogging

class GlobalErrorDecoder : ErrorDecoder {

    private val logger = KotlinLogging.logger {}

    override fun decode(methodKey: String, response: Response): Exception {
        logger.info("{} 요청이 성공하지 못했습니다. status : {}, body : {}", methodKey, response.status(), response.body())
        // 500 에러만 Exception 처리
        when (response.status()) {
            in 500..599 -> throw handle5xxError(response)
        }
        return errorStatus(methodKey, response)
    }

    private fun handle5xxError(response: Response): BusinessException {
        logger.info(
            ":::CustomErrorDecoder::: response: {} status : {}, body : {}",
            response,
            response.status(),
            response.body().asInputStream().bufferedReader().use { it.readText() }
        )
        return BusinessException(ErrorCode.SERVER_ERROR)
    }
}
