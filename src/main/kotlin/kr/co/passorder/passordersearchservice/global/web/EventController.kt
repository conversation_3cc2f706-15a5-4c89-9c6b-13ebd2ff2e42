package kr.co.passorder.passordersearchservice.global.web

import kr.co.passorder.passordersearchservice.global.annotation.CommandHandler
import kr.co.passorder.passordersearchservice.global.annotation.CommandProcessor
import kr.co.passorder.passordersearchservice.global.annotation.EventConsumer
import kr.co.passorder.passordersearchservice.global.annotation.EventHandler
import kr.co.passorder.passordersearchservice.global.annotation.ReplyProcessor
import kr.co.passorder.passordersearchservice.global.web.HandlerType.COMMAND
import kr.co.passorder.passordersearchservice.global.web.HandlerType.EVENT
import org.springframework.context.ApplicationContext
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import javax.annotation.PostConstruct
import kotlin.reflect.KFunction
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.functions
import kotlin.reflect.jvm.javaMethod

@RestController
class EventController(
    private val applicationContext: ApplicationContext
) {

    private val handlers = mutableMapOf<String, MutableMap<String, Pair<HandlerType, KFunction<*>>>>()

    @PostConstruct
    fun scanAndRegisterHandlers() {
        val eventHandlerBeans = applicationContext.getBeansWithAnnotation(EventHandler::class.java)
        eventHandlerBeans.forEach { (_, bean) ->
            val groupId = bean::class.findAnnotation<EventHandler>()?.groupId ?: return@forEach
            bean::class.functions.forEach { method ->
                method.findAnnotation<EventConsumer>()?.let { eventConsumer ->
                    handlers.computeIfAbsent(groupId) { mutableMapOf() }[eventConsumer.topic] = Pair(EVENT, method)
                }
            }
        }

        val commandHandlerBeans = applicationContext.getBeansWithAnnotation(CommandHandler::class.java)
        commandHandlerBeans.forEach { (_, bean) ->
            val groupId = bean::class.findAnnotation<CommandHandler>()?.groupId ?: return@forEach
            bean::class.functions.forEach { method ->
                method.findAnnotation<CommandProcessor>()?.let { commandProcessor ->
                    handlers.computeIfAbsent(groupId) { mutableMapOf() }[commandProcessor.command] =
                        Pair(COMMAND, method)
                }

                method.findAnnotation<ReplyProcessor>()?.let { commandProcessor ->
                    handlers.computeIfAbsent(groupId) { mutableMapOf() }[commandProcessor.reply] =
                        Pair(COMMAND, method)
                }
            }
        }
    }

    @PostMapping("/domain-events/topics/{topic}/groups/{groupId}/consume")
    fun handleRequest(
        @PathVariable topic: String,
        @PathVariable groupId: String,
        @RequestBody body: String
    ): ResponseEntity<Unit> {
        val handler: Pair<HandlerType, KFunction<*>> = handlers[groupId]
            ?.get(topic)
            ?: return ResponseEntity.notFound().build()

        val (handlerType: HandlerType, method: KFunction<*>) = handler
        when (handlerType) {
            EVENT -> {
                val bean = applicationContext.getBean(method.javaMethod!!.declaringClass)
                method.call(bean, body)
            }

            COMMAND -> {
                val bean = applicationContext.getBean(method.javaMethod!!.declaringClass)
                method.call(bean, body)
            }
        }

        return ResponseEntity.ok().build()
    }
}
