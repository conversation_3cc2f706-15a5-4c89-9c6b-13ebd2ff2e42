package kr.co.passorder.passordersearchservice.keyword.adapter.`in`.web

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.adapter.`in`.web.request.CreatePopularKeywordRequest
import kr.co.passorder.passordersearchservice.keyword.adapter.`in`.web.request.UpdatePopularKeywordWeightRequest
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.CreatePopularKeywordUseCase
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.QueryPopularKeywordUseCase
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.UpdatePopularKeywordWeightUseCase
import kr.co.passorder.passordersearchservice.keyword.domain.PopularKeyword
import kr.co.passorder.passordersearchservice.keyword.domain.enums.RegistrationType
import kr.co.passorder.passordersearchservice.keyword.domain.exception.DuplicateKeywordException
import kr.co.passorder.passordersearchservice.keyword.domain.exception.FilteredKeywordException
import kr.co.passorder.passordersearchservice.keyword.domain.exception.PopularKeywordNotFoundException
import kr.co.passorder.passordersearchservice.keyword.domain.factory.PopularKeywordFactory
import kr.co.passorder.passordersearchservice.global.pagination.DefaultQueryResult
import kr.co.passorder.passordersearchservice.global.pagination.QueryResult
import kr.co.passorder.passordersearchservice.global.error.GlobalExceptionHandler
import kr.co.passorder.passordersearchservice.global.error.ErrorMessageSender
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.ValidKeywordUseCase
import kr.co.passorder.passordersearchservice.keyword.domain.exception.KeywordErrorCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import java.util.UUID

@DisplayName("PopularKeywordAdminController 테스트")
class PopularKeywordAdminControllerTest {

    private lateinit var mockMvc: MockMvc
    private lateinit var objectMapper: ObjectMapper
    private lateinit var createPopularKeywordUseCase: CreatePopularKeywordUseCase
    private lateinit var updatePopularKeywordWeightUseCase: UpdatePopularKeywordWeightUseCase
    private lateinit var findPopularKeywordUseCase: QueryPopularKeywordUseCase
    private lateinit var validKeywordUseCase: ValidKeywordUseCase

    @BeforeEach
    fun setUp() {
        createPopularKeywordUseCase = mockk()
        updatePopularKeywordWeightUseCase = mockk()
        findPopularKeywordUseCase = mockk()
        validKeywordUseCase = mockk()
        objectMapper = ObjectMapper()

        val controller = PopularKeywordAdminController(
            createPopularKeywordUseCase,
            updatePopularKeywordWeightUseCase,
            findPopularKeywordUseCase,
            validKeywordUseCase
        )

        // Global Exception Handler 추가
        val errorMessageSender = mockk<ErrorMessageSender>(relaxed = true)
        val globalExceptionHandler = GlobalExceptionHandler(errorMessageSender)

        mockMvc = MockMvcBuilders
            .standaloneSetup(controller)
            .setControllerAdvice(globalExceptionHandler)
            .build()
    }

    @Test
    @DisplayName("인기검색어 등록 여부 체크 성공")
    fun checkAvailablePopularKeyword_shouldReturn200_whenSuccessful() {
        // given
        val keyword = "등록 키워드"

        every { validKeywordUseCase.validatePopularCreation(any()) } just Runs

        // when & then
        mockMvc.perform(
            get("/admin/keywords/popular/availability")
                .param("keyword", keyword)
        )
            .andExpect(status().isOk)

        verify(exactly = 1) { validKeywordUseCase.validatePopularCreation(any()) }
    }

    @Test
    @DisplayName("중복 키워드 등록 시 409 응답")
    fun checkAvailablePopularKeyword_shouldReturn409_whenDuplicateKeyword() {
        // given
        val keyword = "중복키워드"
        val errorCode = KeywordErrorCode.DUPLICATE_KEYWORD

        every { validKeywordUseCase.validatePopularCreation(any()) } throws DuplicateKeywordException()

        // when & then
        mockMvc.perform(
            get("/admin/keywords/popular/availability")
                .param("keyword", keyword)
        )
            .andExpect(status().isConflict)
            .andExpect(jsonPath("$.code").value(errorCode.toString()))

        verify(exactly = 1) { validKeywordUseCase.validatePopularCreation(any()) }
    }

    @Test
    @DisplayName("필터링 대상 키워드 등록 시 409 응답")
    fun checkAvailablePopularKeyword_shouldReturn409_whenFilteredKeyword() {
        // given
        val keyword = "필터검색어"
        val errorCode = KeywordErrorCode.FORBIDDEN_KEYWORD

        every { validKeywordUseCase.validatePopularCreation(any()) } throws FilteredKeywordException(errorCode)

        // when & then
        mockMvc.perform(
            get("/admin/keywords/popular/availability")
                .param("keyword", keyword)
        )
            .andExpect(status().isConflict)
            .andExpect(jsonPath("$.code").value(errorCode.toString()))

        verify(exactly = 1) { validKeywordUseCase.validatePopularCreation(any()) }
    }


    @Test
    @DisplayName("인기검색어 등록 성공")
    fun createPopularKeyword_shouldReturn201_whenSuccessful() {
        // given
        val request = CreatePopularKeywordRequest(
            keyword = "치킨",
            weight = 100
        )
        val createdKeyword: PopularKeyword = PopularKeywordFactory.createManual(
            keyword = "치킨",
            weight = 100
        )

        every { validKeywordUseCase.validatePopularCreation(any()) } just Runs
        every { createPopularKeywordUseCase.create(any()) } returns createdKeyword

        // when & then
        mockMvc.perform(
            post("/admin/keywords/popular")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("인기 검색어가 성공적으로 수동 등록되었습니다."))
            .andExpect(jsonPath("$.data.keyword").value("치킨"))
            .andExpect(jsonPath("$.data.weight").value(100))
            .andExpect(jsonPath("$.data.is_manual").value(true))

        verify(exactly = 1) { validKeywordUseCase.validatePopularCreation(any()) }
        verify(exactly = 1) { createPopularKeywordUseCase.create(any()) }
    }

    @Test
    @DisplayName("중복 키워드 등록 시 409 응답")
    fun createPopularKeyword_shouldReturn409_whenDuplicateKeyword() {
        // given
        val request = CreatePopularKeywordRequest(
            keyword = "치킨",
            weight = 100
        )

        every { validKeywordUseCase.validatePopularCreation(any()) } throws DuplicateKeywordException("이미 등록된 키워드입니다.")

        // when & then
        mockMvc.perform(
            post("/admin/keywords/popular")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isConflict)
            .andExpect(jsonPath("$.code").exists())
            .andExpect(jsonPath("$.message").exists())

        verify(exactly = 1) { validKeywordUseCase.validatePopularCreation(any()) }
        verify(exactly = 0) { createPopularKeywordUseCase.create(any()) }
    }

    @Test
    @DisplayName("필터링 대상 키워드 등록 시 409 응답")
    fun createPopularKeyword_shouldReturn409_whenFilteredKeyword() {
        // given
        val request = CreatePopularKeywordRequest(
            keyword = "금지어",
            weight = 100
        )

        every { validKeywordUseCase.validatePopularCreation(any()) } throws FilteredKeywordException(KeywordErrorCode.STORE_NAME_KEYWORD)

        // when & then
        mockMvc.perform(
            post("/admin/keywords/popular")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isConflict)
            .andExpect(jsonPath("$.code").exists())
            .andExpect(jsonPath("$.message").exists())

        verify(exactly = 1) { validKeywordUseCase.validatePopularCreation(any()) }
        verify(exactly = 0) { createPopularKeywordUseCase.create(any()) }
    }

    @Test
    @DisplayName("잘못된 입력값으로 등록 시 400 응답")
    fun createPopularKeyword_shouldReturn400_whenInvalidInput() {
        // given - 빈 키워드
        val request = CreatePopularKeywordRequest(
            keyword = "",
            weight = 100
        )

        // when & then - Bean Validation에 의해 400 반환
        mockMvc.perform(
            post("/admin/keywords/popular")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isBadRequest)

        // UseCase는 호출되지 않아야 함
        verify(exactly = 0) { createPopularKeywordUseCase.create(any()) }
    }

    @Test
    @DisplayName("인기검색어 상세 조회 성공")
    fun getPopularKeyword_shouldReturn200_whenKeywordExists() {
        // given
        val keywordId: UUID = UUID.randomUUID()
        val keyword: PopularKeyword = PopularKeywordFactory.createManual(
            keyword = "치킨"
        ).apply { 
            // identifier를 설정하기 위해서는 도메인 객체를 수정해야 함
            // 여기서는 Mock으로 업데이트
        }

        every { findPopularKeywordUseCase.findById(keywordId) } returns keyword

        // when & then
        mockMvc.perform(get("/admin/keywords/popular/{keyword_identifier}", keywordId))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.keyword").value("치킨"))
            // identifier 검증은 생략 (도메인 객체에서 UUID가 다름)

        verify(exactly = 1) { findPopularKeywordUseCase.findById(keywordId) }
    }

    @Test
    @DisplayName("존재하지 않는 인기검색어 조회 시 404 응답")
    fun getPopularKeyword_shouldReturn404_whenKeywordNotExists() {
        // given
        val keywordId: UUID = UUID.randomUUID()

        every { findPopularKeywordUseCase.findById(keywordId) } returns null

        // when & then
        mockMvc.perform(get("/admin/keywords/popular/{keyword_identifier}", keywordId))
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.code").exists())
            .andExpect(jsonPath("$.message").exists())

        verify(exactly = 1) { findPopularKeywordUseCase.findById(keywordId) }
    }

    @Test
    @DisplayName("가중치 수정 성공")
    fun updatePopularKeywordWeight_shouldReturn200_whenSuccessful() {
        // given
        val keywordId: UUID = UUID.randomUUID()
        val request = UpdatePopularKeywordWeightRequest(weight = 200)
        val updatedKeyword: PopularKeyword = PopularKeywordFactory.createManual(
            weight = 200, keyword = ""
        )

        every { updatePopularKeywordWeightUseCase.updateWeight(any()) } returns updatedKeyword

        // when & then
        mockMvc.perform(
            put("/admin/keywords/popular/{keyword_identifier}/weight", keywordId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("인기 검색어 가중치가 성공적으로 수정되었습니다."))
            .andExpect(jsonPath("$.data.weight").value(200))

        verify(exactly = 1) { updatePopularKeywordWeightUseCase.updateWeight(any()) }
    }

    @Test
    @DisplayName("존재하지 않는 키워드 가중치 수정 시 404 응답")
    fun updatePopularKeywordWeight_shouldReturn404_whenKeywordNotExists() {
        // given
        val keywordId: UUID = UUID.randomUUID()
        val request = UpdatePopularKeywordWeightRequest(weight = 200)

        every { updatePopularKeywordWeightUseCase.updateWeight(any()) } throws PopularKeywordNotFoundException("인기검색어를 찾을 수 없습니다.")

        // when & then
        mockMvc.perform(
            put("/admin/keywords/popular/{keyword_identifier}/weight", keywordId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.code").exists())
            .andExpect(jsonPath("$.message").exists())

        verify(exactly = 1) { updatePopularKeywordWeightUseCase.updateWeight(any()) }
    }

    @Test
    @DisplayName("인기검색어 검색 목록 조회 성공")
    fun searchPopularKeywords_shouldReturn200_withPaginationResult() {
        // given
        val keywords: List<PopularKeyword> = listOf(
            PopularKeywordFactory.createManual("치킨"),
            PopularKeywordFactory.createManual("피자")
        )
        val queryResult: QueryResult<PopularKeyword> = DefaultQueryResult(
            results = keywords,
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 2,
            hasNext = false
        )

        every { 
            findPopularKeywordUseCase.search(
                any(), any(), any(), any(), any(), any(), any(), any()
            ) 
        } returns queryResult

        // when & then
        mockMvc.perform(
            get("/admin/keywords/popular")
                .param("keyword", "치")
                .param("page", "1")
                .param("limit", "20")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data").isNotEmpty)
            .andExpect(jsonPath("$.total_data_count").value(2))

        verify(exactly = 1) { 
            findPopularKeywordUseCase.search(
                any(), any(), any(), any(), any(), any(), any(), any()
            ) 
        }
    }

    @Test
    @DisplayName("인기검색어 목록 조회 - 필터링 파라미터 적용")
    fun searchPopularKeywords_shouldApplyFilterParameters() {
        // given
        val queryResult: QueryResult<PopularKeyword> = DefaultQueryResult(
            results = emptyList(),
            requestPage = 1,
            requestLimit = 10,
            totalResultsCount = 0,
            hasNext = false
        )

        every { 
            findPopularKeywordUseCase.search(
                keyword = "치킨",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 100,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 10
            ) 
        } returns queryResult

        // when & then
        mockMvc.perform(
            get("/admin/keywords/popular")
                .param("keyword", "치킨")
                .param("registration_type", "MANUAL")
                .param("weight_gte", "100")
                .param("page", "1")
                .param("limit", "10")
        )
            .andExpect(status().isOk)

        verify(exactly = 1) { 
            findPopularKeywordUseCase.search(
                keyword = "치킨",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 100,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 10
            ) 
        }
    }
}
