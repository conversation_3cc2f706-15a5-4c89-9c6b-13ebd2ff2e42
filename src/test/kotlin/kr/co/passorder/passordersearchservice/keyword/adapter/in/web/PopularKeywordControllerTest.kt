package kr.co.passorder.passordersearchservice.keyword.adapter.`in`.web

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.QueryPopularKeywordUseCase
import kr.co.passorder.passordersearchservice.keyword.fixture.PopularKeywordFixture
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.test.web.servlet.setup.MockMvcBuilders

@DisplayName("PopularKeywordController 테스트")
class PopularKeywordControllerTest {

    private lateinit var mockMvc: MockMvc
    private lateinit var objectMapper: ObjectMapper
    private lateinit var findPopularKeywordUseCase: QueryPopularKeywordUseCase

    @BeforeEach
    fun setUp() {
        findPopularKeywordUseCase = mockk()
        objectMapper = ObjectMapper()

        val controller = PopularKeywordController(findPopularKeywordUseCase)
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build()
    }

    @Test
    @DisplayName("인기검색어 조회 성공 - 기본 8개 제한")
    fun getPopularKeywords_shouldReturn200_withDefaultLimit() {
        // given
        val rankedKeywords = PopularKeywordFixture.createDomainWithKeywords(
            listOf("치킨", "피자", "햄버거", "족발", "보쌈", "떡볶이", "김밥", "라면")
        )

        every { findPopularKeywordUseCase.findRankedKeywordForDisplay() } returns rankedKeywords

        // when & then
        mockMvc.perform(get("/keywords/popular"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(8))
            .andExpect(jsonPath("$.data[0].keyword").value("치킨"))
            .andExpect(jsonPath("$.data[0].rank").value(1))
            .andExpect(jsonPath("$.data[1].keyword").value("피자"))
            .andExpect(jsonPath("$.data[1].rank").value(2))

        verify(exactly = 1) { findPopularKeywordUseCase.findRankedKeywordForDisplay() }
    }

    @Test
    @DisplayName("인기검색어 조회 성공 - 사용자 지정 제한")
    fun getPopularKeywords_shouldReturn200_withCustomLimit() {
        // given
        val rankedKeywords = PopularKeywordFixture.createDomainWithKeywords(
            listOf("치킨", "피자", "햄버거", "족발", "보쌈")
        )

        every { findPopularKeywordUseCase.findRankedKeywordForDisplay() } returns rankedKeywords

        // when & then - 3개만 요청
        mockMvc.perform(
            get("/keywords/popular")
                .param("limit", "3")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(3)) // take(3)로 제한됨
            .andExpect(jsonPath("$.data[0].keyword").value("치킨"))
            .andExpect(jsonPath("$.data[1].keyword").value("피자"))
            .andExpect(jsonPath("$.data[2].keyword").value("햄버거"))

        verify(exactly = 1) { findPopularKeywordUseCase.findRankedKeywordForDisplay() }
    }

    @Test
    @DisplayName("인기검색어 조회 - 최대 10개 제한 적용")
    fun getPopularKeywords_shouldLimitTo10_whenRequestingMore() {
        // given
        val rankedKeywords = (1..15).map { 
            PopularKeywordFixture.createDomain(keyword = "키워드$it", rank = it) 
        }

        every { findPopularKeywordUseCase.findRankedKeywordForDisplay() } returns rankedKeywords

        // when & then - 제한 내에서 테스트
        mockMvc.perform(
            get("/keywords/popular")
                .param("limit", "10") // 최대 제한 내
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data.length()").value(10))

        verify(exactly = 1) { findPopularKeywordUseCase.findRankedKeywordForDisplay() }
    }

    @Test
    @DisplayName("인기검색어 조회 - 잘못된 limit 파라미터")
    fun getPopularKeywords_shouldReturn400_whenInvalidLimit() {
        // when & then - 0개 요청 (최소 1개) - Validation 없으므로 통과
        every { findPopularKeywordUseCase.findRankedKeywordForDisplay() } returns emptyList()

        mockMvc.perform(
            get("/keywords/popular")
                .param("limit", "0")
        )
            .andExpect(status().isOk) // Validation이 없으므로 성공

        // when & then - 11개 요청 (최대 10개) - Validation 없으므로 통과
        mockMvc.perform(
            get("/keywords/popular")
                .param("limit", "11")
        )
            .andExpect(status().isOk) // Validation이 없으므로 성공
    }

    @Test
    @DisplayName("인기검색어 조회 - 빈 결과")
    fun getPopularKeywords_shouldReturn200_whenNoKeywords() {
        // given
        every { findPopularKeywordUseCase.findRankedKeywordForDisplay() } returns emptyList()

        // when & then
        mockMvc.perform(get("/keywords/popular"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(0))

        verify(exactly = 1) { findPopularKeywordUseCase.findRankedKeywordForDisplay() }
    }

    @Test
    @DisplayName("인기검색어 조회 - 순위가 있는 키워드만 반환되는지 확인")
    fun getPopularKeywords_shouldOnlyReturnRankedKeywords() {
        // given - 순위가 있는 키워드들만 포함
        val rankedKeywords = PopularKeywordFixture.createDomainWithKeywords(
            listOf("치킨", "피자", "햄버거")
        )

        every { findPopularKeywordUseCase.findRankedKeywordForDisplay() } returns rankedKeywords

        // when & then
        mockMvc.perform(get("/keywords/popular"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(3))
            .andExpect(jsonPath("$.data[0].rank").value(1))
            .andExpect(jsonPath("$.data[1].rank").value(2))
            .andExpect(jsonPath("$.data[2].rank").value(3))

        verify(exactly = 1) { findPopularKeywordUseCase.findRankedKeywordForDisplay() }
    }

    @Test
    @DisplayName("인기검색어 조회 응답 형식 확인")
    fun getPopularKeywords_shouldHaveCorrectResponseFormat() {
        // given
        val keyword = PopularKeywordFixture.createDomain(
            keyword = "치킨",
            rank = 1,
            totalScore = 1000,
            searchCount = 500,
            orderCount = 400,
            weight = 100
        )

        every { findPopularKeywordUseCase.findRankedKeywordForDisplay() } returns listOf(keyword)

        // when & then
        mockMvc.perform(get("/keywords/popular"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data[0].identifier").exists())
            .andExpect(jsonPath("$.data[0].keyword").value("치킨"))
            .andExpect(jsonPath("$.data[0].rank").value(1))
            .andExpect(jsonPath("$.data[0].total_score").value(1000))

        verify(exactly = 1) { findPopularKeywordUseCase.findRankedKeywordForDisplay() }
    }
}
