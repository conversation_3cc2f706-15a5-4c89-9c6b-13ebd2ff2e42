package kr.co.passorder.passordersearchservice.keyword.adapter.`in`.web

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.adapter.`in`.web.request.CreateRecommendedKeywordRequest
import kr.co.passorder.passordersearchservice.keyword.adapter.`in`.web.request.UpdateRecommendedKeywordWeightRequest
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.CreateRecommendedKeywordUseCase
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.QueryRecommendedKeywordUseCase
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.UpdateRecommendedKeywordWeightUseCase
import kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword
import kr.co.passorder.passordersearchservice.keyword.domain.enums.RegistrationType
import kr.co.passorder.passordersearchservice.keyword.domain.exception.DuplicateKeywordException
import kr.co.passorder.passordersearchservice.keyword.domain.exception.FilteredKeywordException
import kr.co.passorder.passordersearchservice.keyword.domain.exception.RecommendedKeywordNotFoundException
import kr.co.passorder.passordersearchservice.keyword.domain.factory.RecommendedKeywordFactory
import kr.co.passorder.passordersearchservice.global.pagination.DefaultQueryResult
import kr.co.passorder.passordersearchservice.global.pagination.QueryResult
import kr.co.passorder.passordersearchservice.global.error.GlobalExceptionHandler
import kr.co.passorder.passordersearchservice.global.error.ErrorMessageSender
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.ValidKeywordUseCase
import kr.co.passorder.passordersearchservice.keyword.domain.exception.KeywordErrorCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import java.util.UUID

@DisplayName("RecommendedKeywordAdminController 테스트")
class RecommendedKeywordAdminControllerTest {

    private lateinit var mockMvc: MockMvc
    private lateinit var objectMapper: ObjectMapper
    private lateinit var createRecommendedKeywordUseCase: CreateRecommendedKeywordUseCase
    private lateinit var updateRecommendedKeywordWeightUseCase: UpdateRecommendedKeywordWeightUseCase
    private lateinit var findRecommendedKeywordUseCase: QueryRecommendedKeywordUseCase
    private lateinit var validKeywordUseCase: ValidKeywordUseCase

    @BeforeEach
    fun setUp() {
        createRecommendedKeywordUseCase = mockk()
        updateRecommendedKeywordWeightUseCase = mockk()
        findRecommendedKeywordUseCase = mockk()
        validKeywordUseCase = mockk()
        objectMapper = ObjectMapper()

        val controller = RecommendedKeywordAdminController(
            createRecommendedKeywordUseCase,
            updateRecommendedKeywordWeightUseCase,
            findRecommendedKeywordUseCase,
            validKeywordUseCase
        )

        // Global Exception Handler 추가
        val errorMessageSender = mockk<ErrorMessageSender>(relaxed = true)
        val globalExceptionHandler = GlobalExceptionHandler(errorMessageSender)

        mockMvc = MockMvcBuilders
            .standaloneSetup(controller)
            .setControllerAdvice(globalExceptionHandler)
            .build()
    }

    @Test
    @DisplayName("추천검색어 등록 가능 여부 체크")
    fun checkAvailableRecommendedKeyword_shouldReturn200_whenSuccessful() {
        // given
        val keyword = "추천검색어"

        every { validKeywordUseCase.validateRecommendedCreation(any()) } just Runs

        // when & then
        mockMvc.perform(
            get("/admin/keywords/recommended/availability")
                .param("keyword", keyword)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.code").value("AVAILABLE"))

        verify(exactly = 1) { validKeywordUseCase.validateRecommendedCreation(any()) }
    }

    @Test
    @DisplayName("중복 키워드 등록 여부 체크 시 409 응답")
    fun checkAvailableRecommendedKeyword_shouldReturn409_whenDuplicateKeyword() {
        // given
        val keyword = "중복검색어"
        val errorCode = KeywordErrorCode.DUPLICATE_KEYWORD

        every { validKeywordUseCase.validateRecommendedCreation(any()) } throws DuplicateKeywordException()

        // when & then
        mockMvc.perform(
            get("/admin/keywords/recommended/availability")
                .param("keyword", keyword)
        )
            .andExpect(status().isConflict)
            .andExpect(jsonPath("$.code").value(errorCode.toString()))

        verify(exactly = 1) { validKeywordUseCase.validateRecommendedCreation(any()) }
    }

    @Test
    @DisplayName("필터링 대상 키워드 등록 여부 체크 시 409 응답")
    fun checkAvailableRecommendedKeyword_shouldReturn409_whenFilteredKeyword() {
        // given
        val keyword = "필터검색어"
        val errorCode = KeywordErrorCode.FORBIDDEN_KEYWORD

        every { validKeywordUseCase.validateRecommendedCreation(any()) } throws FilteredKeywordException(errorCode)

        // when & then
        mockMvc.perform(
            get("/admin/keywords/recommended/availability")
                .param("keyword", keyword)
        )
            .andExpect(status().isConflict)
            .andExpect(jsonPath("$.code").value(errorCode.toString()))

        verify(exactly = 1) { validKeywordUseCase.validateRecommendedCreation(any()) }
    }

    @Test
    @DisplayName("추천검색어 등록 성공")
    fun createRecommendedKeyword_shouldReturn201_whenSuccessful() {
        // given
        val request = CreateRecommendedKeywordRequest(
            keyword = "피자",
            weight = 150
        )
        val createdKeyword: RecommendedKeyword = RecommendedKeywordFactory.createManual(
            keyword = "피자",
            weight = 150
        )

        every { validKeywordUseCase.validateRecommendedCreation(any()) } just Runs
        every { createRecommendedKeywordUseCase.create(any()) } returns createdKeyword

        // when & then
        mockMvc.perform(
            post("/admin/keywords/recommended")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("추천 검색어가 성공적으로 수동 등록되었습니다."))
            .andExpect(jsonPath("$.data.keyword").value("피자"))
            .andExpect(jsonPath("$.data.weight").value(150))

        verify(exactly = 1) { createRecommendedKeywordUseCase.create(any()) }
    }

    @Test
    @DisplayName("중복 키워드 등록 시 409 응답")
    fun createRecommendedKeyword_shouldReturn409_whenDuplicateKeyword() {
        // given
        val request = CreateRecommendedKeywordRequest(
            keyword = "피자",
            weight = 150
        )

        every { validKeywordUseCase.validateRecommendedCreation(any()) } throws DuplicateKeywordException("이미 등록된 키워드입니다.")

        // when & then
        mockMvc.perform(
            post("/admin/keywords/recommended")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isConflict)
            .andExpect(jsonPath("$.code").exists())
            .andExpect(jsonPath("$.message").exists())

        verify(exactly = 1) { validKeywordUseCase.validateRecommendedCreation(any()) }
        verify(exactly = 0) { createRecommendedKeywordUseCase.create(any()) }
    }

    @Test
    @DisplayName("필터링 대상 키워드 등록 시 409 응답")
    fun createRecommendedKeyword_shouldReturn409_whenFilteredKeyword() {
        // given
        val request = CreateRecommendedKeywordRequest(
            keyword = "금지어",
            weight = 150
        )

        every { validKeywordUseCase.validateRecommendedCreation(any()) } throws FilteredKeywordException(
            KeywordErrorCode.RANKED_KEYWORD_RESTRICTION)

        // when & then
        mockMvc.perform(
            post("/admin/keywords/recommended")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isConflict)
            .andExpect(jsonPath("$.code").exists())
            .andExpect(jsonPath("$.message").exists())

        verify(exactly = 1) { validKeywordUseCase.validateRecommendedCreation(any()) }
        verify(exactly = 0) { createRecommendedKeywordUseCase.create(any()) }
    }

    @Test
    @DisplayName("잘못된 입력값으로 등록 시 400 응답")
    fun createRecommendedKeyword_shouldReturn400_whenInvalidInput() {
        // given - 빈 키워드
        val request = CreateRecommendedKeywordRequest(
            keyword = "",
            weight = 150
        )

        // when & then - Bean Validation에 의해 400 반환
        mockMvc.perform(
            post("/admin/keywords/recommended")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isBadRequest)

        // UseCase는 호출되지 않아야 함
        verify(exactly = 0) { createRecommendedKeywordUseCase.create(any()) }
    }

    @Test
    @DisplayName("추천검색어 상세 조회 성공")
    fun getRecommendedKeyword_shouldReturn200_whenKeywordExists() {
        // given
        val keywordId: UUID = UUID.randomUUID()
        val keyword: RecommendedKeyword = RecommendedKeywordFactory.createManual(
            keyword = "피자"
        )

        every { findRecommendedKeywordUseCase.findById(keywordId) } returns keyword

        // when & then
        mockMvc.perform(get("/admin/keywords/recommended/{keyword_identifier}", keywordId))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.keyword").value("피자"))
            // identifier 검증은 생략 (도메인 객체에서 UUID가 다름)

        verify(exactly = 1) { findRecommendedKeywordUseCase.findById(keywordId) }
    }

    @Test
    @DisplayName("존재하지 않는 추천검색어 조회 시 404 응답")
    fun getRecommendedKeyword_shouldReturn404_whenKeywordNotExists() {
        // given
        val keywordId: UUID = UUID.randomUUID()

        every { findRecommendedKeywordUseCase.findById(keywordId) } returns null

        // when & then
        mockMvc.perform(get("/admin/keywords/recommended/{keyword_identifier}", keywordId))
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.code").exists())
            .andExpect(jsonPath("$.message").exists())

        verify(exactly = 1) { findRecommendedKeywordUseCase.findById(keywordId) }
    }

    @Test
    @DisplayName("가중치 수정 성공")
    fun updateRecommendedKeywordWeight_shouldReturn200_whenSuccessful() {
        // given
        val keywordId: UUID = UUID.randomUUID()
        val request = UpdateRecommendedKeywordWeightRequest(weight = 300)
        val updatedKeyword: RecommendedKeyword = RecommendedKeywordFactory.createManual(
            weight = 300, keyword = ""
        )

        every { updateRecommendedKeywordWeightUseCase.updateWeight(any()) } returns updatedKeyword

        // when & then
        mockMvc.perform(
            put("/admin/keywords/recommended/{keyword_identifier}/weight", keywordId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("추천 검색어 가중치가 성공적으로 수정되었습니다."))
            .andExpect(jsonPath("$.data.weight").value(300))

        verify(exactly = 1) { updateRecommendedKeywordWeightUseCase.updateWeight(any()) }
    }

    @Test
    @DisplayName("존재하지 않는 키워드 가중치 수정 시 404 응답")
    fun updateRecommendedKeywordWeight_shouldReturn404_whenKeywordNotExists() {
        // given
        val keywordId: UUID = UUID.randomUUID()
        val request = UpdateRecommendedKeywordWeightRequest(weight = 300)

        every { updateRecommendedKeywordWeightUseCase.updateWeight(any()) } throws RecommendedKeywordNotFoundException("추천검색어를 찾을 수 없습니다.")

        // when & then
        mockMvc.perform(
            put("/admin/keywords/recommended/{keyword_identifier}/weight", keywordId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.code").exists())
            .andExpect(jsonPath("$.message").exists())

        verify(exactly = 1) { updateRecommendedKeywordWeightUseCase.updateWeight(any()) }
    }

    @Test
    @DisplayName("추천검색어 목록 조회 성공")
    fun searchRecommendedKeywords_shouldReturn200_withPaginationResult() {
        // given
        val keywords: List<RecommendedKeyword> = listOf(
            RecommendedKeywordFactory.createManual("피자"),
            RecommendedKeywordFactory.createManual("버거")
        )
        val queryResult: QueryResult<RecommendedKeyword> = DefaultQueryResult(
            results = keywords,
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 2,
            hasNext = false
        )

        every { 
            findRecommendedKeywordUseCase.search(
                any(), any(), any(), any(), any(), any(), any(), any()
            ) 
        } returns queryResult

        // when & then
        mockMvc.perform(
            get("/admin/keywords/recommended")
                .param("keyword", "피")
                .param("page", "1")
                .param("limit", "20")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data").isNotEmpty)
            .andExpect(jsonPath("$.total_data_count").value(2))

        verify(exactly = 1) { 
            findRecommendedKeywordUseCase.search(
                any(), any(), any(), any(), any(), any(), any(), any()
            ) 
        }
    }

    @Test
    @DisplayName("추천검색어 목록 조회 - 필터링 파라미터 적용")
    fun searchRecommendedKeywords_shouldApplyFilterParameters() {
        // given
        val queryResult: QueryResult<RecommendedKeyword> = DefaultQueryResult(
            results = emptyList(),
            requestPage = 1,
            requestLimit = 10,
            totalResultsCount = 0,
            hasNext = false
        )

        every { 
            findRecommendedKeywordUseCase.search(
                keyword = "피자",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 100,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 10
            ) 
        } returns queryResult

        // when & then
        mockMvc.perform(
            get("/admin/keywords/recommended")
                .param("keyword", "피자")
                .param("registration_type", "MANUAL")
                .param("weight_gte", "100")
                .param("page", "1")
                .param("limit", "10")
        )
            .andExpect(status().isOk)

        verify(exactly = 1) { 
            findRecommendedKeywordUseCase.search(
                keyword = "피자",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 100,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 10
            ) 
        }
    }

    @Test
    @DisplayName("추천검색어 목록 조회 - 빈 결과")
    fun searchRecommendedKeywords_shouldReturn200_whenEmptyResult() {
        // given
        val queryResult: QueryResult<RecommendedKeyword> = DefaultQueryResult(
            results = emptyList(),
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 0,
            hasNext = false
        )

        every { 
            findRecommendedKeywordUseCase.search(
                any(), any(), any(), any(), any(), any(), any(), any()
            ) 
        } returns queryResult

        // when & then
        mockMvc.perform(
            get("/admin/keywords/recommended")
                .param("keyword", "존재하지않는키워드")
                .param("page", "1")
                .param("limit", "20")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data").isEmpty)
            .andExpect(jsonPath("$.total_data_count").value(0))

        verify(exactly = 1) { 
            findRecommendedKeywordUseCase.search(
                any(), any(), any(), any(), any(), any(), any(), any()
            ) 
        }
    }
}
