package kr.co.passorder.passordersearchservice.keyword.adapter.`in`.web

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.QueryRecommendedKeywordUseCase
import kr.co.passorder.passordersearchservice.keyword.fixture.RecommendedKeywordFixture
import kr.co.passorder.passordersearchservice.global.pagination.DefaultQueryResult
import kr.co.passorder.passordersearchservice.global.pagination.QueryResult
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.test.web.servlet.setup.MockMvcBuilders

@DisplayName("RecommendedKeywordController 테스트")
class RecommendedKeywordControllerTest {

    private lateinit var mockMvc: MockMvc
    private lateinit var objectMapper: ObjectMapper
    private lateinit var findRecommendedKeywordUseCase: QueryRecommendedKeywordUseCase

    @BeforeEach
    fun setUp() {
        findRecommendedKeywordUseCase = mockk()
        objectMapper = ObjectMapper()

        val controller = RecommendedKeywordController(findRecommendedKeywordUseCase)
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build()
    }

    @Test
    @DisplayName("추천검색어 조회 성공 - 기본 파라미터")
    fun getRecommendedKeywords_shouldReturn200_withDefaultParameters() {
        // given
        val keywords = RecommendedKeywordFixture.createDomainWithKeywords(
            listOf("파스타", "스시", "피자", "샐러드", "리조또")
        )
        val queryResult: QueryResult<kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword> = DefaultQueryResult(
            results = keywords,
            requestPage = 1,
            requestLimit = 10,
            totalResultsCount = 5,
            hasNext = false
        )

        every { findRecommendedKeywordUseCase.findForDisplay(1, 10) } returns queryResult

        // when & then
        mockMvc.perform(get("/keywords/recommended"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(5))
            .andExpect(jsonPath("$.data[0].keyword").value("파스타"))
            .andExpect(jsonPath("$.data[1].keyword").value("스시"))
            .andExpect(jsonPath("$.total_data_count").value(5))
            .andExpect(jsonPath("$.max_page").value(1))
            .andExpect(jsonPath("$.has_next").value(false))

        verify(exactly = 1) { findRecommendedKeywordUseCase.findForDisplay(1, 10) }
    }

    @Test
    @DisplayName("추천검색어 조회 성공 - 사용자 지정 파라미터")
    fun getRecommendedKeywords_shouldReturn200_withCustomParameters() {
        // given
        val keywords = RecommendedKeywordFixture.createDomainWithKeywords(
            listOf("파스타", "스시", "피자")
        )
        val queryResult: QueryResult<kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword> = DefaultQueryResult(
            results = keywords,
            requestPage = 2,
            requestLimit = 5,
            totalResultsCount = 15,
            hasNext = true
        )

        every { findRecommendedKeywordUseCase.findForDisplay(2, 5) } returns queryResult

        // when & then
        mockMvc.perform(
            get("/keywords/recommended")
                .param("page", "2")
                .param("limit", "5")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(3))
            .andExpect(jsonPath("$.total_data_count").value(15))
            .andExpect(jsonPath("$.max_page").value(3))
            .andExpect(jsonPath("$.has_next").value(true))

        verify(exactly = 1) { findRecommendedKeywordUseCase.findForDisplay(2, 5) }
    }

    @Test
    @DisplayName("추천검색어 조회 - 잘못된 파라미터")
    fun getRecommendedKeywords_shouldReturn400_whenInvalidParameters() {
        // Validation이 없으므로 모든 파라미터가 통과됨
        every { findRecommendedKeywordUseCase.findForDisplay(any(), any()) } returns DefaultQueryResult(
            results = emptyList(),
            requestPage = 0,
            requestLimit = 0,
            totalResultsCount = 0,
            hasNext = false
        )

        // when & then - page 0 (최소 1) - Validation 없으므로 통과
        mockMvc.perform(
            get("/keywords/recommended")
                .param("page", "0")
        )
            .andExpect(status().isOk)

        verify(exactly = 1) { findRecommendedKeywordUseCase.findForDisplay(0, 10) }
    }

    @Test
    @DisplayName("추천검색어 조회 - 빈 결과")
    fun getRecommendedKeywords_shouldReturn200_whenNoKeywords() {
        // given
        val queryResult: QueryResult<kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword> = DefaultQueryResult(
            results = emptyList(),
            requestPage = 1,
            requestLimit = 10,
            totalResultsCount = 0,
            hasNext = false
        )

        every { findRecommendedKeywordUseCase.findForDisplay(1, 10) } returns queryResult

        // when & then
        mockMvc.perform(get("/keywords/recommended"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(0))
            .andExpect(jsonPath("$.total_data_count").value(0))

        verify(exactly = 1) { findRecommendedKeywordUseCase.findForDisplay(1, 10) }
    }

    @Test
    @DisplayName("랜덤 추천검색어 조회 성공 - 기본 5개")
    fun getRandomRecommendedKeywords_shouldReturn200_withDefaultCount() {
        // given
        val randomKeywords = RecommendedKeywordFixture.createDomainWithKeywords(
            listOf("랜덤1", "랜덤2", "랜덤3", "랜덤4", "랜덤5")
        )

        every { findRecommendedKeywordUseCase.findRandomKeywords(5) } returns randomKeywords

        // when & then
        mockMvc.perform(get("/keywords/recommended/random"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(5))
            .andExpect(jsonPath("$.data[0].keyword").value("랜덤1"))
            .andExpect(jsonPath("$.data[1].keyword").value("랜덤2"))

        verify(exactly = 1) { findRecommendedKeywordUseCase.findRandomKeywords(5) }
    }

    @Test
    @DisplayName("랜덤 추천검색어 조회 성공 - 사용자 지정 개수")
    fun getRandomRecommendedKeywords_shouldReturn200_withCustomCount() {
        // given
        val randomKeywords = RecommendedKeywordFixture.createDomainWithKeywords(
            listOf("랜덤1", "랜덤2", "랜덤3")
        )

        every { findRecommendedKeywordUseCase.findRandomKeywords(3) } returns randomKeywords

        // when & then
        mockMvc.perform(
            get("/keywords/recommended/random")
                .param("count", "3")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(3))

        verify(exactly = 1) { findRecommendedKeywordUseCase.findRandomKeywords(3) }
    }

    @Test
    @DisplayName("랜덤 추천검색어 조회 - 잘못된 count 파라미터")
    fun getRandomRecommendedKeywords_shouldReturn400_whenInvalidCount() {
        // Validation이 없으므로 모든 파라미터가 통과됨
        every { findRecommendedKeywordUseCase.findRandomKeywords(any()) } returns emptyList()

        // when & then - count 0 (최소 1) - Validation 없으므로 통과
        mockMvc.perform(
            get("/keywords/recommended/random")
                .param("count", "0")
        )
            .andExpect(status().isOk)

        verify(exactly = 1) { findRecommendedKeywordUseCase.findRandomKeywords(0) }
    }

    @Test
    @DisplayName("랜덤 추천검색어 조회 - 빈 결과")
    fun getRandomRecommendedKeywords_shouldReturn200_whenNoKeywords() {
        // given
        every { findRecommendedKeywordUseCase.findRandomKeywords(5) } returns emptyList()

        // when & then
        mockMvc.perform(get("/keywords/recommended/random"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(0))

        verify(exactly = 1) { findRecommendedKeywordUseCase.findRandomKeywords(5) }
    }

    @Test
    @DisplayName("추천검색어 조회 응답 형식 확인")
    fun getRecommendedKeywords_shouldHaveCorrectResponseFormat() {
        // given
        val keyword = RecommendedKeywordFixture.createDomain(
            keyword = "파스타",
            totalScore = 800,
            searchCount = 400,
            searchResultCount = 200,
            weight = 200
        )
        val queryResult: QueryResult<kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword> = DefaultQueryResult(
            results = listOf(keyword),
            requestPage = 1,
            requestLimit = 10,
            totalResultsCount = 1,
            hasNext = false
        )

        every { findRecommendedKeywordUseCase.findForDisplay(1, 10) } returns queryResult

        // when & then
        mockMvc.perform(get("/keywords/recommended"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data[0].identifier").exists())
            .andExpect(jsonPath("$.data[0].keyword").value("파스타"))
            .andExpect(jsonPath("$.data[0].total_score").value(800))
            .andExpect(jsonPath("$.data[0].search_result_count").value(200))

        verify(exactly = 1) { findRecommendedKeywordUseCase.findForDisplay(1, 10) }
    }
}
