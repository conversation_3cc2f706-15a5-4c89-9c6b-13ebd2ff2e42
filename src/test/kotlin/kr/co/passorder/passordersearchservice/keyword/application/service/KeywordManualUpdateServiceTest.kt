package kr.co.passorder.passordersearchservice.keyword.application.service

import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.UpdatePopularKeywordStatisticsUseCase
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.UpdateRecommendedKeywordStatisticsUseCase
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.command.UpdateKeywordStatisticsCommand
import kr.co.passorder.passordersearchservice.keyword.application.port.out.command.ActionRecordIngestCommand
import kr.co.passorder.passordersearchservice.keyword.domain.enums.KeywordUpdateType
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
@DisplayName("KeywordManualUpdateService 테스트")
class KeywordManualUpdateServiceTest {

    @MockK
    private lateinit var updatePopularKeywordStatisticsUseCase: UpdatePopularKeywordStatisticsUseCase

    @MockK
    private lateinit var updateRecommendedKeywordStatisticsUseCase: UpdateRecommendedKeywordStatisticsUseCase

    @InjectMockKs
    private lateinit var keywordManualUpdateService: KeywordManualUpdateService

    @Nested
    @DisplayName("수동 키워드 업데이트 실행")
    inner class ExecuteManualUpdate {

        @Test
        @DisplayName("인기검색어만 업데이트할 때 올바르게 처리되어야 한다")
        fun shouldUpdatePopularKeywordsOnly() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.POPULAR,
                baseDate = baseDate
            )
            
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit

            // when
            val result = keywordManualUpdateService.executeManualUpdate(command)

            // then
            assertThat(result.popularKeywordResult).isNotNull()
            assertThat(result.recommendedKeywordResult).isNull()
            assertThat(result.startedAt).isNotNull()
            assertThat(result.completedAt).isNotNull()
            assertThat(result.completedAt).isAfter(result.startedAt)
            
            verify(exactly = 1) { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
            verify(exactly = 0) { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
        }

        @Test
        @DisplayName("추천검색어만 업데이트할 때 올바르게 처리되어야 한다")
        fun shouldUpdateRecommendedKeywordsOnly() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.RECOMMENDED,
                baseDate = baseDate
            )
            
            every { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit

            // when
            val result = keywordManualUpdateService.executeManualUpdate(command)

            // then
            assertThat(result.popularKeywordResult).isNull()
            assertThat(result.recommendedKeywordResult).isNotNull()
            assertThat(result.startedAt).isNotNull()
            assertThat(result.completedAt).isNotNull()
            assertThat(result.completedAt).isAfter(result.startedAt)
            
            verify(exactly = 0) { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
            verify(exactly = 1) { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
        }

        @Test
        @DisplayName("전체 키워드 업데이트할 때 인기검색어와 추천검색어 모두 처리되어야 한다")
        fun shouldUpdateAllKeywords() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.ALL,
                baseDate = baseDate
            )
            
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit
            every { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit

            // when
            val result = keywordManualUpdateService.executeManualUpdate(command)

            // then
            assertThat(result.popularKeywordResult).isNotNull()
            assertThat(result.recommendedKeywordResult).isNotNull()
            assertThat(result.startedAt).isNotNull()
            assertThat(result.completedAt).isNotNull()
            assertThat(result.completedAt).isAfter(result.startedAt)
            
            verify(exactly = 1) { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
            verify(exactly = 1) { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
        }

        @Test
        @DisplayName("지원하지 않는 키워드 타입일 때 예외가 발생해야 한다")
        fun shouldThrowExceptionForUnsupportedKeywordType() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Undefined("UNSUPPORTED"),
                baseDate = baseDate
            )

            // when & then
            assertThatThrownBy {
                keywordManualUpdateService.executeManualUpdate(command)
            }.isInstanceOf(IllegalArgumentException::class.java)
                .hasMessageContaining("지원하지 않는 키워드 타입입니다")
            
            verify(exactly = 0) { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
            verify(exactly = 0) { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
        }

        @Test
        @DisplayName("인기검색어 업데이트 중 예외 발생 시 예외가 전파되어야 한다")
        fun shouldPropagateExceptionWhenPopularKeywordUpdateFails() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.POPULAR,
                baseDate = baseDate
            )
            
            val expectedException = RuntimeException("Database connection failed")
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } throws expectedException

            // when & then
            assertThatThrownBy {
                keywordManualUpdateService.executeManualUpdate(command)
            }.isEqualTo(expectedException)
        }

        @Test
        @DisplayName("추천검색어 업데이트 중 예외 발생 시 예외가 전파되어야 한다")
        fun shouldPropagateExceptionWhenRecommendedKeywordUpdateFails() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.RECOMMENDED,
                baseDate = baseDate
            )
            
            val expectedException = RuntimeException("ElasticSearch timeout")
            every { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } throws expectedException

            // when & then
            assertThatThrownBy {
                keywordManualUpdateService.executeManualUpdate(command)
            }.isEqualTo(expectedException)
        }

        @Test
        @DisplayName("전체 업데이트 중 인기검색어 업데이트가 실패하면 추천검색어 업데이트는 실행되지 않아야 한다")
        fun shouldNotExecuteRecommendedUpdateWhenPopularUpdateFailsInAllMode() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.ALL,
                baseDate = baseDate
            )
            
            val expectedException = RuntimeException("Popular keyword update failed")
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } throws expectedException

            // when & then
            assertThatThrownBy {
                keywordManualUpdateService.executeManualUpdate(command)
            }.isEqualTo(expectedException)
            
            verify(exactly = 1) { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
            verify(exactly = 0) { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
        }

        @Test
        @DisplayName("전체 업데이트 중 추천검색어 업데이트가 실패하면 예외가 전파되어야 한다")
        fun shouldPropagateExceptionWhenRecommendedUpdateFailsInAllMode() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.ALL,
                baseDate = baseDate
            )
            
            val expectedException = RuntimeException("Recommended keyword update failed")
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit
            every { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } throws expectedException

            // when & then
            assertThatThrownBy {
                keywordManualUpdateService.executeManualUpdate(command)
            }.isEqualTo(expectedException)
            
            verify(exactly = 1) { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
            verify(exactly = 1) { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) }
        }
    }

    @Nested
    @DisplayName("키워드 업데이트 세부 처리")
    inner class KeywordUpdateDetails {

        @Test
        @DisplayName("인기검색어 업데이트 시 올바른 ActionRecordIngestCommand가 생성되어야 한다")
        fun shouldCreateCorrectActionRecordIngestCommandForPopular() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.POPULAR,
                baseDate = baseDate
            )
            
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit

            // when
            keywordManualUpdateService.executeManualUpdate(command)

            // then
            verify { 
                updatePopularKeywordStatisticsUseCase.updateStatistics(
                    match<ActionRecordIngestCommand> { it.baseDate == baseDate }
                )
            }
        }

        @Test
        @DisplayName("추천검색어 업데이트 시 올바른 ActionRecordIngestCommand가 생성되어야 한다")
        fun shouldCreateCorrectActionRecordIngestCommandForRecommended() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.RECOMMENDED,
                baseDate = baseDate
            )
            
            every { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit

            // when
            keywordManualUpdateService.executeManualUpdate(command)

            // then
            verify { 
                updateRecommendedKeywordStatisticsUseCase.updateStatistics(
                    match<ActionRecordIngestCommand> { it.baseDate == baseDate }
                )
            }
        }

        @Test
        @DisplayName("인기검색어 업데이트 결과가 올바른 기본값을 포함해야 한다")
        fun shouldReturnCorrectDefaultValuesForPopularKeywordResult() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.POPULAR,
                baseDate = baseDate
            )
            
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit

            // when
            val result = keywordManualUpdateService.executeManualUpdate(command)

            // then
            assertThat(result.popularKeywordResult).isNotNull()
            result.popularKeywordResult?.let { detail ->
                assertThat(detail.topRankCount).isEqualTo(8)
                assertThat(detail.processingTimeMs).isGreaterThanOrEqualTo(0)
                assertThat(detail.collectedCount).isEqualTo(0) // 임시 기본값
                assertThat(detail.filteredCount).isEqualTo(0) // 임시 기본값
                assertThat(detail.updatedCount).isEqualTo(0) // 임시 기본값
            }
        }

        @Test
        @DisplayName("추천검색어 업데이트 결과가 올바른 기본값을 포함해야 한다")
        fun shouldReturnCorrectDefaultValuesForRecommendedKeywordResult() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.RECOMMENDED,
                baseDate = baseDate
            )
            
            every { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } returns Unit

            // when
            val result = keywordManualUpdateService.executeManualUpdate(command)

            // then
            assertThat(result.recommendedKeywordResult).isNotNull()
            result.recommendedKeywordResult?.let { detail ->
                assertThat(detail.topRankCount).isEqualTo(0) // 추천검색어는 topRankCount 0
                assertThat(detail.processingTimeMs).isGreaterThanOrEqualTo(0)
                assertThat(detail.collectedCount).isEqualTo(0) // 임시 기본값
                assertThat(detail.filteredCount).isEqualTo(0) // 임시 기본값
                assertThat(detail.updatedCount).isEqualTo(0) // 임시 기본값
            }
        }
    }

    @Nested
    @DisplayName("시간 측정 및 로깅")
    inner class TimingAndLogging {

        @Test
        @DisplayName("처리 시간이 올바르게 측정되어야 한다")
        fun shouldMeasureProcessingTimeCorrectly() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.POPULAR,
                baseDate = baseDate
            )
            
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } answers {
                Thread.sleep(10) // 의도적인 지연
            }

            // when
            val startTime = System.currentTimeMillis()
            val result = keywordManualUpdateService.executeManualUpdate(command)
            val endTime = System.currentTimeMillis()

            // then
            assertThat(result.popularKeywordResult?.processingTimeMs).isGreaterThan(0)
            assertThat(result.completedAt).isAfter(result.startedAt)
            assertThat(endTime - startTime).isGreaterThanOrEqualTo(10)
        }

        @Test
        @DisplayName("전체 업데이트 시 각각의 처리 시간이 측정되어야 한다")
        fun shouldMeasureBothProcessingTimesForAllUpdate() {
            // given
            val baseDate = OffsetDateTime.now()
            val command = UpdateKeywordStatisticsCommand(
                keywordType = KeywordUpdateType.Defined.ALL,
                baseDate = baseDate
            )
            
            every { updatePopularKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } answers {
                Thread.sleep(5)
            }
            every { updateRecommendedKeywordStatisticsUseCase.updateStatistics(any<ActionRecordIngestCommand>()) } answers {
                Thread.sleep(5)
            }

            // when
            val result = keywordManualUpdateService.executeManualUpdate(command)

            // then
            assertThat(result.popularKeywordResult?.processingTimeMs).isGreaterThan(0)
            assertThat(result.recommendedKeywordResult?.processingTimeMs).isGreaterThan(0)
            assertThat(result.completedAt).isAfter(result.startedAt)
        }
    }
}