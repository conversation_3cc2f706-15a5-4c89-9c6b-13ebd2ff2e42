package kr.co.passorder.passordersearchservice.keyword.application.service

import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindPopularKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindRecommendedKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.application.port.out.SavePopularKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.application.port.out.SaveRecommendedKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.domain.PopularKeyword
import kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword
import kr.co.passorder.passordersearchservice.keyword.domain.common.KeywordConstants.POPULAR_TOP_RANK_SIZE
import kr.co.passorder.passordersearchservice.keyword.domain.factory.PopularKeywordFactory
import kr.co.passorder.passordersearchservice.keyword.domain.factory.RecommendedKeywordFactory
import kr.co.passorder.passordersearchservice.keyword.domain.vo.UpdateKeywordRankingResult
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
@DisplayName("KeywordRankingService 테스트")
class KeywordRankingServiceTest {

    @MockK
    private lateinit var findPopularKeywordOutput: FindPopularKeywordOutput

    @MockK
    private lateinit var findRecommendedKeywordOutput: FindRecommendedKeywordOutput

    @MockK
    private lateinit var savePopularKeywordOutput: SavePopularKeywordOutput

    @MockK
    private lateinit var saveRecommendedKeywordOutput: SaveRecommendedKeywordOutput

    @InjectMockKs
    private lateinit var keywordRankingService: KeywordRankingService

    @Nested
    @DisplayName("키워드 랭킹 업데이트 및 동기화")
    inner class UpdateKeywordRankingsAndSynchronize {

        @Test
        @DisplayName("새로운 랭킹 키워드들이 올바른 순위로 업데이트되어야 한다")
        fun shouldUpdateNewRankingKeywordsWithCorrectRanks() {
            // given
            val topKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 100),
                PopularKeywordFactory.createManual("피자", 80),
                PopularKeywordFactory.createManual("햄버거", 60)
            )
            
            every { findPopularKeywordOutput.findKeywordsByTotalScoreDesc(POPULAR_TOP_RANK_SIZE) } returns topKeywords
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns emptyList()
            every { findRecommendedKeywordOutput.findAllWithPopularRankTrue() } returns emptyList()
            every { findRecommendedKeywordOutput.findByKeywordIn(any())} returns emptyList()
            every { savePopularKeywordOutput.saveAll(any()) } returns topKeywords
            every { saveRecommendedKeywordOutput.saveAll(any()) } returns emptyList()

            // when
            val result = keywordRankingService.updateKeywordRankingsAndSynchronize()

            // then
            assertThat(result.rankingKeyword).hasSize(3)
            assertThat(result.rankingKeyword[0].rank).isEqualTo(1)
            assertThat(result.rankingKeyword[1].rank).isEqualTo(2)
            assertThat(result.rankingKeyword[2].rank).isEqualTo(3)
            assertThat(result.popularUpdateCount).isEqualTo(3)
            assertThat(result.recommendedUpdateCount).isEqualTo(0)
        }

        @Test
        @DisplayName("기존 랭킹 키워드의 순위가 변경되었을 때 업데이트되어야 한다")
        fun shouldUpdateExistingKeywordsWhenRankChanged() {
            // given
            val currentTopKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 120).apply { updateRank(1) },
                PopularKeywordFactory.createManual("피자", 100).apply { updateRank(2) }
            )
            
            val previousRankedKeywords = listOf(
                PopularKeywordFactory.createManual("피자", 80).apply { updateRank(1) },
                PopularKeywordFactory.createManual("치킨", 70).apply { updateRank(2) }
            )

            every { findPopularKeywordOutput.findKeywordsByTotalScoreDesc(POPULAR_TOP_RANK_SIZE) } returns currentTopKeywords
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns previousRankedKeywords
            every { findRecommendedKeywordOutput.findAllWithPopularRankTrue() } returns emptyList()
            every { findRecommendedKeywordOutput.findByKeywordIn(any()) } returns emptyList()
            every { savePopularKeywordOutput.saveAll(any()) } returns currentTopKeywords
            every { saveRecommendedKeywordOutput.saveAll(any()) } returns emptyList()

            // when
            val result = keywordRankingService.updateKeywordRankingsAndSynchronize()

            // then
            assertThat(result.rankingKeyword[0].keyword).isEqualTo("치킨")
            assertThat(result.rankingKeyword[0].rank).isEqualTo(1)
            assertThat(result.rankingKeyword[1].keyword).isEqualTo("피자")
            assertThat(result.rankingKeyword[1].rank).isEqualTo(2)
            assertThat(result.popularUpdateCount).isEqualTo(2)
        }

        @Test
        @DisplayName("랭킹에서 제외된 키워드의 순위가 null로 설정되어야 한다")
        fun shouldSetRankToNullForKeywordsRemovedFromRanking() {
            // given - 순위 변경이 없도록 동일한 순위로 설정
            val currentTopKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 100)
            )
            
            val previousRankedKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 90).apply { updateRank(1) },
                PopularKeywordFactory.createManual("피자", 80).apply { updateRank(2) },
                PopularKeywordFactory.createManual("하노비", 70).apply { updateRank(3) }
            )

            val savedKeywordsSlot = slot<List<PopularKeyword>>()

            every { findPopularKeywordOutput.findKeywordsByTotalScoreDesc(POPULAR_TOP_RANK_SIZE) } returns currentTopKeywords
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns previousRankedKeywords
            every { findRecommendedKeywordOutput.findAllWithPopularRankTrue() } returns emptyList()
            every { findRecommendedKeywordOutput.findByKeywordIn(any()) } returns emptyList()
            every { savePopularKeywordOutput.saveAll(capture(savedKeywordsSlot)) } answers {
                val keywordsToSave = savedKeywordsSlot.captured
                keywordsToSave
            }
            every { saveRecommendedKeywordOutput.saveAll(any()) } returns emptyList()

            // when
            val result = keywordRankingService.updateKeywordRankingsAndSynchronize()

            // then
            // 피자와 하노비가 랭킹에서 제외되어 rank=null로 설정됨
            assertThat(result.popularUpdateCount).isGreaterThanOrEqualTo(2)
            
            // 저장된 키워드들을 검증
            val savedKeywords = savedKeywordsSlot.captured
            assertThat(savedKeywords.size).isGreaterThanOrEqualTo(2)
            
            // 랭킹에서 제외된 키워드들의 rank가 null로 설정되었는지 확인
            val demotedKeywords = savedKeywords.filter { it.rank == null }
            assertThat(demotedKeywords.size).isGreaterThanOrEqualTo(2)
            
            val demotedKeywordNames = demotedKeywords.map { it.keyword }.toSet()
            assertThat(demotedKeywordNames).contains("피자")
            assertThat(demotedKeywordNames).contains("하노비")
            
            verify { savePopularKeywordOutput.saveAll(any()) }
        }

        @Test
        @DisplayName("추천검색어의 인기검색어 상태가 올바르게 동기화되어야 한다")
        fun shouldSynchronizeRecommendedKeywordPopularStatus() {
            // given
            val currentTopKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 100)
            )
            
            val currentExcludedRecommended = listOf(
                RecommendedKeywordFactory.createManual("피자", 80).apply { updatePopularRankStatus(true) }
            )

            every { findPopularKeywordOutput.findKeywordsByTotalScoreDesc(POPULAR_TOP_RANK_SIZE) } returns currentTopKeywords
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns emptyList()
            every { findRecommendedKeywordOutput.findAllWithPopularRankTrue() } returns currentExcludedRecommended
            every { findRecommendedKeywordOutput.findByKeywordIn(setOf("치킨")) } returns listOf(
                RecommendedKeywordFactory.createManual("치킨", 90)
            )
            every { savePopularKeywordOutput.saveAll(any()) } returns currentTopKeywords
            every { saveRecommendedKeywordOutput.saveAll(any()) } answers {
                val keywords = arg<List<RecommendedKeyword>>(0)
                // 치킨: popularRank true로 변경
                // 피자: popularRank false로 변경
                assertThat(keywords).hasSize(2)
                keywords
            }

            // when
            val result = keywordRankingService.updateKeywordRankingsAndSynchronize()

            // then
            assertThat(result.recommendedUpdateCount).isEqualTo(2)
            verify { saveRecommendedKeywordOutput.saveAll(any()) }
        }

        @Test
        @DisplayName("추천검색어 상태가 이미 정합성을 유지하고 있으면 업데이트하지 않아야 한다")
        fun shouldNotUpdateWhenRecommendedStatusAlreadyConsistent() {
            // given
            val currentTopKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 100)
            )
            
            val currentExcludedRecommended = listOf(
                RecommendedKeywordFactory.createManual("치킨", 90).apply { updatePopularRankStatus(true) }
            )

            every { findPopularKeywordOutput.findKeywordsByTotalScoreDesc(POPULAR_TOP_RANK_SIZE) } returns currentTopKeywords
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns emptyList()
            every { findRecommendedKeywordOutput.findAllWithPopularRankTrue() } returns currentExcludedRecommended
            every { savePopularKeywordOutput.saveAll(any()) } returns currentTopKeywords
            every { saveRecommendedKeywordOutput.saveAll(any()) } returns emptyList()

            // when
            val result = keywordRankingService.updateKeywordRankingsAndSynchronize()

            // then
            assertThat(result.recommendedUpdateCount).isEqualTo(0)
            verify(exactly = 0) { findRecommendedKeywordOutput.findByKeywordIn(any()) }
        }

        @Test
        @DisplayName("인기검색어와 추천검색어 모두 업데이트가 필요한 복합 상황을 처리해야 한다")
        fun shouldHandleComplexScenarioWithBothUpdatesNeeded() {
            // given
            val currentTopKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 120),
                PopularKeywordFactory.createManual("피자", 100)
            )
            
            val previousRankedKeywords = listOf(
                PopularKeywordFactory.createManual("햄버거", 90).apply { updateRank(1) },
                PopularKeywordFactory.createManual("치킨", 80).apply { updateRank(2) }
            )
            
            val currentExcludedRecommended = listOf(
                RecommendedKeywordFactory.createManual("햄버거", 85).apply { updatePopularRankStatus(true) }
            )

            every { findPopularKeywordOutput.findKeywordsByTotalScoreDesc(POPULAR_TOP_RANK_SIZE) } returns currentTopKeywords
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns previousRankedKeywords
            every { findRecommendedKeywordOutput.findAllWithPopularRankTrue() } returns currentExcludedRecommended
            every { findRecommendedKeywordOutput.findByKeywordIn(setOf("치킨", "피자")) } returns listOf(
                RecommendedKeywordFactory.createManual("치킨", 75),
                RecommendedKeywordFactory.createManual("피자", 70)
            )
            every { savePopularKeywordOutput.saveAll(any()) } returns listOf(
                currentTopKeywords[0], currentTopKeywords[1], 
                PopularKeywordFactory.createManual("햄버거", 90).apply { updateRank(null) }
            )
            every { saveRecommendedKeywordOutput.saveAll(any()) } returns listOf(
                RecommendedKeywordFactory.createManual("치킨", 75).apply { updatePopularRankStatus(true) },
                RecommendedKeywordFactory.createManual("피자", 70).apply { updatePopularRankStatus(true) },
                RecommendedKeywordFactory.createManual("햄버거", 85).apply { updatePopularRankStatus(false) }
            )

            // when
            val result = keywordRankingService.updateKeywordRankingsAndSynchronize()

            // then
            assertThat(result.rankingKeyword).hasSize(2)
            assertThat(result.popularUpdateCount).isEqualTo(3) // 치킨, 피자, 햄버거
            assertThat(result.recommendedUpdateCount).isEqualTo(3) // 치킨, 피자, 햄버거
        }
    }

    @Nested
    @DisplayName("결과 객체 검증")
    inner class ResultValidation {

        @Test
        @DisplayName("UpdateKeywordRankingResult가 올바른 정보를 포함해야 한다")
        fun shouldReturnCorrectResultInfo() {
            // given
            val topKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 100)
            )
            
            every { findPopularKeywordOutput.findKeywordsByTotalScoreDesc(POPULAR_TOP_RANK_SIZE) } returns topKeywords
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns emptyList()
            every { findRecommendedKeywordOutput.findAllWithPopularRankTrue() } returns emptyList()
            every { findRecommendedKeywordOutput.findByKeywordIn(any()) } returns emptyList()
            every { savePopularKeywordOutput.saveAll(any()) } returns topKeywords
            every { saveRecommendedKeywordOutput.saveAll(any()) } returns emptyList()

            // when
            val result = keywordRankingService.updateKeywordRankingsAndSynchronize()

            // then
            assertThat(result).isInstanceOf(UpdateKeywordRankingResult::class.java)
            assertThat(result.rankingKeyword).isNotEmpty()
            assertThat(result.popularUpdateCount).isGreaterThanOrEqualTo(0)
            assertThat(result.recommendedUpdateCount).isGreaterThanOrEqualTo(0)
        }
    }
}