package kr.co.passorder.passordersearchservice.keyword.application.service

import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindKeywordFilterRuleOutput
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindPopularKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindRecommendedKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.domain.KeywordFilterRule
import kr.co.passorder.passordersearchservice.keyword.domain.exception.DuplicateKeywordException
import kr.co.passorder.passordersearchservice.keyword.domain.exception.FilteredKeywordException
import kr.co.passorder.passordersearchservice.keyword.domain.factory.PopularKeywordFactory
import org.assertj.core.api.Assertions.assertThatCode
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
@DisplayName("KeywordValidateService 테스트")
class KeywordValidateServiceTest {

    @MockK
    private lateinit var findKeywordFilterRuleOutput: FindKeywordFilterRuleOutput

    @MockK
    private lateinit var findPopularKeywordOutput: FindPopularKeywordOutput

    @MockK
    private lateinit var findRecommendedKeywordOutput: FindRecommendedKeywordOutput

    @InjectMockKs
    private lateinit var keywordValidateService: KeywordValidateService

    @BeforeEach
    fun setUp() {
    }

    @Nested
    @DisplayName("인기검색어 생성 검증")
    inner class ValidatePopularCreation {

        @Test
        @DisplayName("유효한 키워드일 때 예외가 발생하지 않아야 한다")
        fun shouldNotThrowExceptionForValidKeyword() {
            // given
            val keyword = "치킨"
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findPopularKeywordOutput.existsByKeyword(keyword) } returns false

            // when & then
            assertThatCode {
                keywordValidateService.validatePopularCreation(keyword)
            }.doesNotThrowAnyException()

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify { findPopularKeywordOutput.existsByKeyword(keyword) }
        }

        @Test
        @DisplayName("매장 키워드일 때 FilteredKeywordException이 발생해야 한다")
        fun shouldThrowFilteredKeywordExceptionWhenStoreKeyword() {
            // given
            val keyword = "맥도날드"
            val filterRule = mockk<KeywordFilterRule> {
                every { isStoreKeyword() } returns true
                every { isBannedWord() } returns false
                every { isSpamKeyword() } returns false
            }
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns filterRule

            // when & then
            assertThatThrownBy {
                keywordValidateService.validatePopularCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify(exactly = 0) { findPopularKeywordOutput.existsByKeyword(any()) }
        }

        @Test
        @DisplayName("금지어일 때 FilteredKeywordException이 발생해야 한다")
        fun shouldThrowFilteredKeywordExceptionWhenBannedWord() {
            // given
            val keyword = "금지어"
            val filterRule = mockk<KeywordFilterRule> {
                every { isStoreKeyword() } returns false
                every { isBannedWord() } returns true
                every { isSpamKeyword() } returns false
            }
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns filterRule

            // when & then
            assertThatThrownBy {
                keywordValidateService.validatePopularCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
        }

        @Test
        @DisplayName("스팸 키워드일 때 FilteredKeywordException이 발생해야 한다")
        fun shouldThrowFilteredKeywordExceptionWhenSpamKeyword() {
            // given
            val keyword = "스팸키워드"
            val filterRule = mockk<KeywordFilterRule> {
                every { isStoreKeyword() } returns false
                every { isBannedWord() } returns false
                every { isSpamKeyword() } returns true
            }
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns filterRule

            // when & then
            assertThatThrownBy {
                keywordValidateService.validatePopularCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
        }

        @Test
        @DisplayName("유효하지 않은 패턴의 키워드일 때 FilteredKeywordException이 발생해야 한다")
        fun shouldThrowFilteredKeywordExceptionForInvalidPattern() {
            // given
            val keyword = "1"
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null

            // when & then
            assertThatThrownBy {
                keywordValidateService.validatePopularCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify(exactly = 0) { findPopularKeywordOutput.existsByKeyword(any()) }
        }

        @Test
        @DisplayName("이미 존재하는 키워드일 때 DuplicateKeywordException이 발생해야 한다")
        fun shouldThrowDuplicateKeywordExceptionWhenKeywordAlreadyExists() {
            // given
            val keyword = "치킨"
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findPopularKeywordOutput.existsByKeyword(keyword) } returns true

            // when & then
            assertThatThrownBy {
                keywordValidateService.validatePopularCreation(keyword)
            }.isInstanceOf(DuplicateKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify { findPopularKeywordOutput.existsByKeyword(keyword) }
        }

        @Test
        @DisplayName("검증 순서가 올바르게 수행되어야 한다 - 필터 타입 먼저")
        fun shouldCheckFilterTypeFirst() {
            // given
            val keyword = "맥도날드"
            val filterRule = mockk<KeywordFilterRule> {
                every { isStoreKeyword() } returns true
            }
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns filterRule

            // when & then
            assertThatThrownBy {
                keywordValidateService.validatePopularCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify(exactly = 0) { findPopularKeywordOutput.existsByKeyword(any()) }
        }
    }

    @Nested
    @DisplayName("추천검색어 생성 검증")
    inner class ValidateRecommendedCreation {

        @Test
        @DisplayName("유효한 키워드일 때 예외가 발생하지 않아야 한다")
        fun shouldNotThrowExceptionForValidKeyword() {
            // given
            val keyword = "치킨"
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findRecommendedKeywordOutput.existsByKeyword(keyword) } returns false
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns emptyList()

            // when & then
            assertThatCode {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.doesNotThrowAnyException()

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify { findRecommendedKeywordOutput.existsByKeyword(keyword) }
            verify { findPopularKeywordOutput.findKeywordsWithRankAscending() }
        }

        @Test
        @DisplayName("필터링 대상 키워드일 때 FilteredKeywordException이 발생해야 한다")
        fun shouldThrowFilteredKeywordExceptionWhenKeywordShouldBeFiltered() {
            // given
            val keyword = "금지어"
            val filterRule = mockk<KeywordFilterRule> {
                every { isStoreKeyword() } returns false
                every { isBannedWord() } returns true
                every { isSpamKeyword() } returns false
            }
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns filterRule

            // when & then
            assertThatThrownBy {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify(exactly = 0) { findRecommendedKeywordOutput.existsByKeyword(any()) }
            verify(exactly = 0) { findPopularKeywordOutput.findKeywordsWithRankAscending() }
        }

        @Test
        @DisplayName("이미 존재하는 추천검색어일 때 DuplicateKeywordException이 발생해야 한다")
        fun shouldThrowDuplicateKeywordExceptionWhenRecommendedKeywordAlreadyExists() {
            // given
            val keyword = "치킨"
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findRecommendedKeywordOutput.existsByKeyword(keyword) } returns true

            // when & then
            assertThatThrownBy {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.isInstanceOf(DuplicateKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify { findRecommendedKeywordOutput.existsByKeyword(keyword) }
            verify(exactly = 0) { findPopularKeywordOutput.findKeywordsWithRankAscending() }
        }

        @Test
        @DisplayName("인기검색어 순위에 든 키워드일 때 FilteredKeywordException이 발생해야 한다")
        fun shouldThrowFilteredKeywordExceptionWhenKeywordIsInPopularRanking() {
            // given
            val keyword = "치킨"
            val rankedPopularKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 100).apply { updateRank(1) },
                PopularKeywordFactory.createManual("피자", 80).apply { updateRank(2) }
            )
            
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findRecommendedKeywordOutput.existsByKeyword(keyword) } returns false
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns rankedPopularKeywords

            // when & then
            assertThatThrownBy {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify { findRecommendedKeywordOutput.existsByKeyword(keyword) }
            verify { findPopularKeywordOutput.findKeywordsWithRankAscending() }
        }

        @Test
        @DisplayName("인기검색어 순위에 없는 키워드는 통과해야 한다")
        fun shouldPassWhenKeywordIsNotInPopularRanking() {
            // given
            val keyword = "햄버거"
            val rankedPopularKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 100).apply { updateRank(1) },
                PopularKeywordFactory.createManual("피자", 80).apply { updateRank(2) }
            )
            
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findRecommendedKeywordOutput.existsByKeyword(keyword) } returns false
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns rankedPopularKeywords

            // when & then
            assertThatCode {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.doesNotThrowAnyException()

            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify { findRecommendedKeywordOutput.existsByKeyword(keyword) }
            verify { findPopularKeywordOutput.findKeywordsWithRankAscending() }
        }

        @Test
        @DisplayName("순위가 null인 인기검색어는 제외해야 한다")
        fun shouldExcludePopularKeywordsWithNullRank() {
            // given
            val keyword = "치킨"
            val popularKeywords = listOf(
                PopularKeywordFactory.createManual("피자", 80).apply { updateRank(1) }
            )
            
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findRecommendedKeywordOutput.existsByKeyword(keyword) } returns false
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns popularKeywords

            // when & then
            assertThatCode {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.doesNotThrowAnyException()
        }

        @Test
        @DisplayName("검증 순서가 올바르게 수행되어야 한다")
        fun shouldFollowCorrectValidationOrder() {
            // given
            val keyword = "금지어"
            val filterRule = mockk<KeywordFilterRule> {
                every { isStoreKeyword() } returns false
                every { isBannedWord() } returns true
                every { isSpamKeyword() } returns false
            }
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns filterRule

            // when & then
            assertThatThrownBy {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)

            // 필터 타입 체크가 먼저 실행되어야 함
            verify { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) }
            verify(exactly = 0) { findRecommendedKeywordOutput.existsByKeyword(any()) }
            verify(exactly = 0) { findPopularKeywordOutput.findKeywordsWithRankAscending() }
        }
    }

    @Nested
    @DisplayName("공통 검증 로직")
    inner class CommonValidationLogic {

        @Test
        @DisplayName("빈 인기검색어 순위 목록일 때 통과해야 한다")
        fun shouldPassWhenPopularRankingIsEmpty() {
            // given
            val keyword = "치킨"
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findRecommendedKeywordOutput.existsByKeyword(keyword) } returns false
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns emptyList()

            // when & then
            assertThatCode {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.doesNotThrowAnyException()
        }

        @Test
        @DisplayName("여러 인기검색어 중 일치하는 키워드가 있으면 예외가 발생해야 한다")
        fun shouldThrowExceptionWhenMatchFoundAmongMultiplePopularKeywords() {
            // given
            val keyword = "피자"
            val rankedPopularKeywords = listOf(
                PopularKeywordFactory.createManual("치킨", 100).apply { updateRank(1) },
                PopularKeywordFactory.createManual("피자", 90).apply { updateRank(2) },
                PopularKeywordFactory.createManual("햄버거", 80).apply { updateRank(3) }
            )
            
            every { findKeywordFilterRuleOutput.findFilterKeywordType(keyword) } returns null
            every { findRecommendedKeywordOutput.existsByKeyword(keyword) } returns false
            every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns rankedPopularKeywords

            // when & then
            assertThatThrownBy {
                keywordValidateService.validateRecommendedCreation(keyword)
            }.isInstanceOf(FilteredKeywordException::class.java)
        }
    }
}
