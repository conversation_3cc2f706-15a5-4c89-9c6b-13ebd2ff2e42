package kr.co.passorder.passordersearchservice.keyword.application.service

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kr.co.passorder.passordersearchservice.global.pagination.DefaultQueryResult
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindPopularKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.domain.PopularKeyword
import kr.co.passorder.passordersearchservice.keyword.domain.enums.RegistrationType
import kr.co.passorder.passordersearchservice.keyword.fixture.PopularKeywordFixture
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import java.util.UUID

@DisplayName("PopularKeywordQueryService 테스트")
class PopularKeywordQueryServiceTest {

    private lateinit var popularKeywordQueryService: PopularKeywordQueryService
    private lateinit var findPopularKeywordOutput: FindPopularKeywordOutput
    private lateinit var rankingService: KeywordRankingService

    @BeforeEach
    fun setUp() {
        findPopularKeywordOutput = mockk()
        popularKeywordQueryService = PopularKeywordQueryService(findPopularKeywordOutput)
        rankingService = mockk()
    }

    @Test
    @DisplayName("ID로 인기검색어 조회 성공")
    fun findById_shouldReturnPopularKeyword_whenExists() {
        // given
        val keywordId = UUID.randomUUID()
        val keyword = PopularKeywordFixture.createDomain(identifier = keywordId, keyword = "치킨")

        every { findPopularKeywordOutput.findById(keywordId) } returns keyword

        // when
        val result = popularKeywordQueryService.findById(keywordId)

        // then
        assertThat(result).isNotNull
        assertThat(result!!.keyword).isEqualTo("치킨")
        assertThat(result.identifier).isEqualTo(keywordId)
        verify { findPopularKeywordOutput.findById(keywordId) }
    }

    @Test
    @DisplayName("ID로 인기검색어 조회 - 존재하지 않음")
    fun findById_shouldReturnNull_whenNotExists() {
        // given
        val keywordId = UUID.randomUUID()

        every { findPopularKeywordOutput.findById(keywordId) } returns null

        // when
        val result = popularKeywordQueryService.findById(keywordId)

        // then
        assertThat(result).isNull()
        verify { findPopularKeywordOutput.findById(keywordId) }
    }

    @Test
    @DisplayName("랭킹 기준 인기검색어 조회 성공 - 변경된 메소드명 사용")
    fun findRankedKeywordForDisplay_shouldReturnRankedKeywords() {
        // given
        val rankedKeywords = PopularKeywordFixture.createDomainWithKeywords(
            listOf("치킨", "피자", "햄버거")
        )

        every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns rankedKeywords

        // when
        val results = popularKeywordQueryService.findRankedKeywordForDisplay()

        // then
        assertThat(results).hasSize(3)
        assertThat(results.map { it.keyword }).containsExactly("치킨", "피자", "햄버거")
        assertThat(results.map { it.rank }).containsExactly(1, 2, 3)

        // 변경된 메소드명이 정확히 호출되었는지 확인
        verify { findPopularKeywordOutput.findKeywordsWithRankAscending() }
    }

    @Test
    @DisplayName("빈 랭킹 결과 조회")
    fun findRankedKeywordForDisplay_shouldReturnEmptyList_whenNoRankedKeywords() {
        // given
        every { findPopularKeywordOutput.findKeywordsWithRankAscending() } returns emptyList()

        // when
        val results = popularKeywordQueryService.findRankedKeywordForDisplay()

        // then
        assertThat(results).isEmpty()
        verify { findPopularKeywordOutput.findKeywordsWithRankAscending() }
    }

    @Test
    @DisplayName("관리자용 검색 - 전체 조건 적용")
    fun search_shouldApplyAllFilters() {
        // given
        val keywords = listOf(
            PopularKeywordFixture.createDomain(keyword = "치킨", weight = 100, totalScore = 300),
            PopularKeywordFixture.createDomain(keyword = "피자", weight = 80, totalScore = 250)
        )
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = keywords,
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 2,
            hasNext = false
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = "치",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 50,
                weightLte = 150,
                totalScoreGte = 200,
                totalScoreLte = 400,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(
            keyword = "치",
            registrationType = RegistrationType.Defined.MANUAL,
            weightGte = 50,
            weightLte = 150,
            totalScoreGte = 200,
            totalScoreLte = 400,
            page = 1,
            limit = 20
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.totalResultsCount).isEqualTo(2)
        assertThat(result.requestPage).isEqualTo(1)
        assertThat(result.requestLimit).isEqualTo(20)
        assertThat(result.hasNext).isFalse()

        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = "치",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 50,
                weightLte = 150,
                totalScoreGte = 200,
                totalScoreLte = 400,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("관리자용 검색 - 기본값 사용")
    fun search_shouldUseDefaultValues() {
        // given
        val keywords = listOf(PopularKeywordFixture.createDomain())
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = keywords,
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 1,
            hasNext = false
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search()

        // then
        assertThat(result.results).hasSize(1)
        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("자동 등록 타입으로 검색")
    fun search_shouldFilterByAutomaticType() {
        // given
        val automaticKeywords = listOf(
            PopularKeywordFixture.createDomain(keyword = "자동키워드1", isManual = false),
            PopularKeywordFixture.createDomain(keyword = "자동키워드2", isManual = false)
        )
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = automaticKeywords,
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 2,
            hasNext = false
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = RegistrationType.Defined.AUTOMATIC,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(
            registrationType = RegistrationType.Defined.AUTOMATIC
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.all { !it.isManual }).isTrue()
        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = RegistrationType.Defined.AUTOMATIC,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("수동 등록 타입으로 검색")
    fun search_shouldFilterByManualType() {
        // given
        val manualKeywords = listOf(
            PopularKeywordFixture.createDomain(keyword = "수동키워드1", isManual = true),
            PopularKeywordFixture.createDomain(keyword = "수동키워드2", isManual = true)
        )
        val queryResult = DefaultQueryResult(manualKeywords, 1, 20, 2, false)

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(
            registrationType = RegistrationType.Defined.MANUAL
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.all { it.isManual }).isTrue()
        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("키워드 부분 검색")
    fun search_shouldSearchByKeywordContains() {
        // given
        val foundKeywords = listOf(
            PopularKeywordFixture.createDomain(keyword = "치킨버거"),
            PopularKeywordFixture.createDomain(keyword = "치킨덮밥")
        )
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = foundKeywords,
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 2,
            hasNext = false
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = "치킨",
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(keyword = "치킨")

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.map { it.keyword }).allMatch { it.contains("치킨") }
        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = "치킨",
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("가중치 범위로 검색")
    fun search_shouldFilterByWeightRange() {
        // given
        val keywords = listOf(
            PopularKeywordFixture.createDomain(keyword = "키워드1", weight = 80),
            PopularKeywordFixture.createDomain(keyword = "키워드2", weight = 120)
        )
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = keywords,
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 2,
            hasNext = false
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = 50,
                weightLte = 150,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(
            weightGte = 50,
            weightLte = 150
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.map { it.weight }).allMatch { it!! >= 50 && it <= 150 }
        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = 50,
                weightLte = 150,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("총점 범위로 검색")
    fun search_shouldFilterByTotalScoreRange() {
        // given
        val keywords = listOf(
            PopularKeywordFixture.createDomain(keyword = "키워드1", totalScore = 250),
            PopularKeywordFixture.createDomain(keyword = "키워드2", totalScore = 350)
        )
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = keywords,
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 2,
            hasNext = false
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = 200,
                totalScoreLte = 400,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(
            totalScoreGte = 200,
            totalScoreLte = 400
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.map { it.totalScore }).allMatch { it >= 200 && it <= 400 }
        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = 200,
                totalScoreLte = 400,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("페이징 처리 확인")
    fun search_shouldHandlePagination() {
        // given
        val keywords = listOf(PopularKeywordFixture.createDomain())
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = keywords,
            requestPage = 2,
            requestLimit = 10,
            totalResultsCount = 15,
            hasNext = true
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 2,
                limit = 10
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(page = 2, limit = 10)

        // then
        assertThat(result.requestPage).isEqualTo(2)
        assertThat(result.requestLimit).isEqualTo(10)
        assertThat(result.totalResultsCount).isEqualTo(15)
        assertThat(result.hasNext).isTrue()
        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 2,
                limit = 10
            )
        }
    }

    @Test
    @DisplayName("복합 조건 검색")
    fun search_shouldApplyMultipleFilters() {
        // given
        val keywords = listOf(
            PopularKeywordFixture.createDomain(
                keyword = "치킨",
                weight = 100,
                totalScore = 300,
                isManual = true
            )
        )
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = keywords,
            requestPage = 1,
            requestLimit = 5,
            totalResultsCount = 1,
            hasNext = false
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = "치",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 80,
                weightLte = 120,
                totalScoreGte = 250,
                totalScoreLte = 350,
                page = 1,
                limit = 5
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(
            keyword = "치",
            registrationType = RegistrationType.Defined.MANUAL,
            weightGte = 80,
            weightLte = 120,
            totalScoreGte = 250,
            totalScoreLte = 350,
            page = 1,
            limit = 5
        )

        // then
        assertThat(result.results).hasSize(1)
        assertThat(result.results.first().keyword).contains("치")
        assertThat(result.results.first().isManual).isTrue()
        assertThat(result.results.first().weight).isBetween(80, 120)
        assertThat(result.results.first().totalScore).isBetween(250, 350)
        assertThat(result.requestLimit).isEqualTo(5)

        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = "치",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 80,
                weightLte = 120,
                totalScoreGte = 250,
                totalScoreLte = 350,
                page = 1,
                limit = 5
            )
        }
    }

    @Test
    @DisplayName("검색 결과 없음")
    fun search_shouldReturnEmptyResult_whenNoMatch() {
        // given
        val queryResult = DefaultQueryResult<PopularKeyword>(
            results = emptyList(),
            requestPage = 1,
            requestLimit = 20,
            totalResultsCount = 0,
            hasNext = false
        )

        every {
            findPopularKeywordOutput.findAllForSearch(
                keyword = "존재하지않는키워드",
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = popularKeywordQueryService.search(keyword = "존재하지않는키워드")

        // then
        assertThat(result.results).isEmpty()
        assertThat(result.totalResultsCount).isEqualTo(0)
        assertThat(result.hasNext).isFalse()

        verify {
            findPopularKeywordOutput.findAllForSearch(
                keyword = "존재하지않는키워드",
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }
}
