package kr.co.passorder.passordersearchservice.keyword.application.service

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.command.CreatePopularKeywordCommand
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.command.UpdatePopularKeywordWeightCommand
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindPopularKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.application.port.out.SavePopularKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.domain.exception.PopularKeywordNotFoundException
import kr.co.passorder.passordersearchservice.keyword.domain.vo.UpdateKeywordRankingResult
import kr.co.passorder.passordersearchservice.keyword.fixture.PopularKeywordFixture
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.UUID

@DisplayName("PopularKeywordService 테스트")
class PopularKeywordServiceTest {

    private lateinit var popularKeywordService: PopularKeywordService
    private lateinit var findPopularKeywordOutput: FindPopularKeywordOutput
    private lateinit var savePopularKeywordOutput: SavePopularKeywordOutput
    private lateinit var keywordRankingService: KeywordRankingService

    @BeforeEach
    fun setUp() {
        findPopularKeywordOutput = mockk()
        savePopularKeywordOutput = mockk()
        keywordRankingService = mockk()

        popularKeywordService = PopularKeywordService(
            findPopularKeywordOutput = findPopularKeywordOutput,
            savePopularKeywordOutput = savePopularKeywordOutput,
            rankingService = keywordRankingService
        )
    }

    @Test
    @DisplayName("인기검색어 생성 성공")
    fun create_shouldCreatePopularKeywordSuccessfully() {
        // given
        val command = CreatePopularKeywordCommand(
            keyword = "치킨",
            weight = 100
        )
        val savedKeyword = PopularKeywordFixture.createDomain(keyword = "치킨", weight = 100, isManual = true)
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = listOf(savedKeyword),
            popularUpdateCount = 1,
            recommendedUpdateCount = 0
        )

        every { savePopularKeywordOutput.save(any()) } returns savedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("치킨")
        assertThat(result.weight).isEqualTo(100)
        assertThat(result.isManual).isTrue()
        
        verify { savePopularKeywordOutput.save(any()) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("인기검색어 생성 - Factory 패턴 사용 확인")
    fun create_shouldUseFactoryPattern() {
        // given
        val command = CreatePopularKeywordCommand(
            keyword = "피자",
            weight = 150
        )
        val savedKeyword = PopularKeywordFixture.createDomain(keyword = "피자", weight = 150, isManual = true)
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = emptyList(),
            popularUpdateCount = 0,
            recommendedUpdateCount = 0
        )

        every { savePopularKeywordOutput.save(any()) } returns savedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("피자")
        assertThat(result.weight).isEqualTo(150)
        assertThat(result.isManual).isTrue() // Factory에서 수동 등록으로 생성됨
        
        // PopularKeywordFactory.createManual()로 생성된 객체가 저장되는지 확인
        verify { savePopularKeywordOutput.save(match { 
            it.keyword == "피자" && it.weight == 150 && it.isManual 
        }) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("가중치 수정 성공")
    fun updateWeight_shouldUpdateWeightSuccessfully() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdatePopularKeywordWeightCommand(
            identifier = keywordId,
            weight = 200
        )
        val existingKeyword = PopularKeywordFixture.createDomain(
            identifier = keywordId, 
            keyword = "치킨",
            weight = 100, 
            searchCount = 60, 
            orderCount = 100
        )
        val beforeTotalScore = existingKeyword.totalScore
        
        // 가중치 업데이트 후의 키워드 (도메인 로직에 의해 totalScore 재계산됨)
        val updatedKeyword = PopularKeywordFixture.createDomain(
            identifier = keywordId,
            keyword = "치킨",
            weight = 200,
            totalScore = existingKeyword.searchCount!! + existingKeyword.orderCount!! + 200
        )
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = listOf(updatedKeyword),
            popularUpdateCount = 1,
            recommendedUpdateCount = 0
        )

        every { findPopularKeywordOutput.findById(keywordId) } returns existingKeyword
        every { savePopularKeywordOutput.save(any()) } returns updatedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(200)
        assertThat(result.totalScore).isGreaterThan(beforeTotalScore)
        
        verify { findPopularKeywordOutput.findById(keywordId) }
        verify { savePopularKeywordOutput.save(any()) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("존재하지 않는 키워드 가중치 수정 시 예외 발생")  
    fun updateWeight_shouldThrowNotFoundException_whenKeywordNotExists() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdatePopularKeywordWeightCommand(
            identifier = keywordId,
            weight = 200
        )

        every { findPopularKeywordOutput.findById(keywordId) } returns null

        // when & then
        assertThrows<PopularKeywordNotFoundException> {
            popularKeywordService.updateWeight(command)
        }

        verify { findPopularKeywordOutput.findById(keywordId) }
        verify(exactly = 0) { savePopularKeywordOutput.save(any()) }
        verify(exactly = 0) { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("가중치 수정 후 도메인 객체 상태 변경 확인")
    fun updateWeight_shouldUpdateDomainObjectState() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdatePopularKeywordWeightCommand(
            identifier = keywordId,
            weight = 300
        )
        val originalKeyword = PopularKeywordFixture.createDomain(
            identifier = keywordId,
            keyword = "햄버거",
            weight = 50,
            searchCount = 100,
            orderCount = 50
        )
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = emptyList(),
            popularUpdateCount = 0,
            recommendedUpdateCount = 0
        )

        every { findPopularKeywordOutput.findById(keywordId) } returns originalKeyword
        every { savePopularKeywordOutput.save(any()) } answers {
            val keyword = firstArg<kr.co.passorder.passordersearchservice.keyword.domain.PopularKeyword>()
            // 도메인 객체의 updateWeight 메소드가 호출된 후의 상태를 시뮬레이션
            PopularKeywordFixture.createDomain(
                identifier = keyword.identifier,
                keyword = keyword.keyword,
                weight = keyword.weight,
                totalScore = keyword.searchCount!! + keyword.orderCount!! + keyword.weight!!
            )
        }
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(300)
        assertThat(result.totalScore).isEqualTo(100 + 50 + 300) // searchCount + orderCount + weight
        
        // 도메인 객체의 updateWeight 메소드가 호출되는지 확인
        verify { savePopularKeywordOutput.save(match { 
            it.weight == 300 
        }) }
    }

    @Test
    @DisplayName("키워드 랭킹 동기화 - 순위 업데이트 결과 반영 확인")
    fun create_shouldSynchronizeKeywordRanking() {
        // given
        val command = CreatePopularKeywordCommand(
            keyword = "새키워드",
            weight = 500
        )
        val savedKeyword = PopularKeywordFixture.createDomain(keyword = "새키워드", weight = 500)
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = listOf(
                PopularKeywordFixture.createDomain(keyword = "새키워드", rank = 1),
                PopularKeywordFixture.createDomain(keyword = "기존키워드", rank = 2)
            ),
            popularUpdateCount = 2,
            recommendedUpdateCount = 1 // 추천검색어도 영향받음
        )

        every { savePopularKeywordOutput.save(any()) } returns savedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        popularKeywordService.create(command)

        // then
        // 랭킹 동기화가 호출되었는지 확인
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
        
        // 순위 재계산 결과: 인기검색어 2개, 추천검색어 1개 업데이트
        assertThat(rankingResult.popularUpdateCount).isEqualTo(2)
        assertThat(rankingResult.recommendedUpdateCount).isEqualTo(1)
    }

    @Test
    @DisplayName("빈 가중치로 키워드 생성")
    fun create_shouldCreateWithNullWeight() {
        // given
        val command = CreatePopularKeywordCommand(
            keyword = "자동키워드",
            weight = null // 가중치 없음
        )
        val savedKeyword = PopularKeywordFixture.createDomain(
            keyword = "자동키워드", 
            weight = null,
            isManual = true // Factory.createManual()는 항상 수동 등록
        )
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = emptyList(),
            popularUpdateCount = 0,
            recommendedUpdateCount = 0
        )

        every { savePopularKeywordOutput.save(any()) } returns savedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("자동키워드")
        assertThat(result.weight).isNull()
        assertThat(result.isManual).isTrue() // Factory 패턴에 의해 수동 등록으로 처리
        
        verify { savePopularKeywordOutput.save(any()) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("키워드 생성 후 트랜잭션 처리 확인")
    fun create_shouldHandleTransactionProperly() {
        // given
        val command = CreatePopularKeywordCommand(
            keyword = "트랜잭션테스트",
            weight = 100
        )
        val savedKeyword = PopularKeywordFixture.createDomain(keyword = "트랜잭션테스트", weight = 100)
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = emptyList(),
            popularUpdateCount = 0,
            recommendedUpdateCount = 0
        )

        every { savePopularKeywordOutput.save(any()) } returns savedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.create(command)

        // then
        assertThat(result).isNotNull
        
        // 호출 순서 확인: 먼저 저장, 그 다음 랭킹 동기화
        verify { savePopularKeywordOutput.save(any()) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("가중치가 0인 키워드 생성")
    fun create_shouldCreateWithZeroWeight() {
        // given
        val command = CreatePopularKeywordCommand(
            keyword = "제로가중치",
            weight = 0
        )
        val savedKeyword = PopularKeywordFixture.createDomain(keyword = "제로가중치", weight = 0)
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = emptyList(),
            popularUpdateCount = 0,
            recommendedUpdateCount = 0
        )

        every { savePopularKeywordOutput.save(any()) } returns savedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("제로가중치")
        assertThat(result.weight).isEqualTo(0)
        
        verify { savePopularKeywordOutput.save(any()) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("음수 가중치로 키워드 생성")
    fun create_shouldCreateWithNegativeWeight() {
        // given
        val command = CreatePopularKeywordCommand(
            keyword = "음수가중치",
            weight = -50
        )
        val savedKeyword = PopularKeywordFixture.createDomain(keyword = "음수가중치", weight = -50)
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = emptyList(),
            popularUpdateCount = 0,
            recommendedUpdateCount = 0
        )

        every { savePopularKeywordOutput.save(any()) } returns savedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("음수가중치")
        assertThat(result.weight).isEqualTo(-50)
        
        verify { savePopularKeywordOutput.save(any()) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("큰 가중치 값으로 키워드 생성")
    fun create_shouldCreateWithLargeWeight() {
        // given
        val command = CreatePopularKeywordCommand(
            keyword = "큰가중치",
            weight = 999999
        )
        val savedKeyword = PopularKeywordFixture.createDomain(keyword = "큰가중치", weight = 999999)
        val rankedKeyword = PopularKeywordFixture.createDomain(
            keyword = savedKeyword.keyword,
            weight = savedKeyword.weight,
            rank = 1
        )
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = listOf(rankedKeyword), // 높은 가중치로 1위 예상
            popularUpdateCount = 1,
            recommendedUpdateCount = 0
        )

        every { savePopularKeywordOutput.save(any()) } returns savedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("큰가중치")
        assertThat(result.weight).isEqualTo(999999)
        
        verify { savePopularKeywordOutput.save(any()) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }

    @Test
    @DisplayName("가중치 수정을 0으로 변경")
    fun updateWeight_shouldUpdateToZero() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdatePopularKeywordWeightCommand(
            identifier = keywordId,
            weight = 0
        )
        val existingKeyword = PopularKeywordFixture.createDomain(identifier = keywordId, weight = 100)
        val updatedKeyword = PopularKeywordFixture.createDomain(
            identifier = keywordId,
            keyword = existingKeyword.keyword,
            weight = 0,
            totalScore = existingKeyword.searchCount!! + existingKeyword.orderCount!! + 0
        )
        val rankingResult = UpdateKeywordRankingResult(
            rankingKeyword = emptyList(),
            popularUpdateCount = 0,
            recommendedUpdateCount = 0
        )

        every { findPopularKeywordOutput.findById(keywordId) } returns existingKeyword
        every { savePopularKeywordOutput.save(any()) } returns updatedKeyword
        every { keywordRankingService.updateKeywordRankingsAndSynchronize() } returns rankingResult

        // when
        val result = popularKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(0)
        
        verify { findPopularKeywordOutput.findById(keywordId) }
        verify { savePopularKeywordOutput.save(any()) }
        verify { keywordRankingService.updateKeywordRankingsAndSynchronize() }
    }
}
