package kr.co.passorder.passordersearchservice.keyword.application.service

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kr.co.passorder.passordersearchservice.global.pagination.DefaultQueryResult
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindRecommendedKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword
import kr.co.passorder.passordersearchservice.keyword.domain.enums.RegistrationType
import kr.co.passorder.passordersearchservice.keyword.fixture.RecommendedKeywordFixture
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import java.util.UUID

@DisplayName("RecommendedKeywordQueryService 테스트")
class RecommendedKeywordQueryServiceTest {

    private lateinit var recommendedKeywordQueryService: RecommendedKeywordQueryService
    private lateinit var findRecommendedKeywordOutput: FindRecommendedKeywordOutput

    @BeforeEach
    fun setUp() {
        findRecommendedKeywordOutput = mockk()
        recommendedKeywordQueryService = RecommendedKeywordQueryService(findRecommendedKeywordOutput)
    }

    @Test
    @DisplayName("ID로 추천검색어 조회 성공")
    fun findById_shouldReturnRecommendedKeyword_whenExists() {
        // given
        val keywordId = UUID.randomUUID()
        val keyword = RecommendedKeywordFixture.createDomain(identifier = keywordId, keyword = "피자")

        every { findRecommendedKeywordOutput.findById(keywordId) } returns keyword

        // when
        val result = recommendedKeywordQueryService.findById(keywordId)

        // then
        assertThat(result).isNotNull
        assertThat(result!!.keyword).isEqualTo("피자")
        assertThat(result.identifier).isEqualTo(keywordId)
        verify { findRecommendedKeywordOutput.findById(keywordId) }
    }

    @Test
    @DisplayName("ID로 추천검색어 조회 - 존재하지 않음")
    fun findById_shouldReturnNull_whenNotExists() {
        // given
        val keywordId = UUID.randomUUID()

        every { findRecommendedKeywordOutput.findById(keywordId) } returns null

        // when
        val result = recommendedKeywordQueryService.findById(keywordId)

        // then
        assertThat(result).isNull()
        verify { findRecommendedKeywordOutput.findById(keywordId) }
    }

    @Test
    @DisplayName("노출 가능한 추천검색어 페이징 조회 성공")
    fun findForDisplay_shouldReturnPagedResults() {
        // given
        val keywords = RecommendedKeywordFixture.createEligibleForDisplayList(5)
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 1L,
            requestLimit = 10,
            totalResultsCount = 5L,
            hasNext = false
        )

        every { findRecommendedKeywordOutput.findAllEligibleForDisplay(1, 10) } returns queryResult

        // when
        val result = recommendedKeywordQueryService.findForDisplay(page = 1, limit = 10)

        // then
        assertThat(result.results).hasSize(5)
        assertThat(result.requestPage).isEqualTo(1)
        assertThat(result.requestLimit).isEqualTo(10)
        assertThat(result.totalResultsCount).isEqualTo(5)
        assertThat(result.hasNext).isFalse()

        verify { findRecommendedKeywordOutput.findAllEligibleForDisplay(1, 10) }
    }

    @Test
    @DisplayName("빈 페이지 조회")
    fun findForDisplay_shouldReturnEmptyResults_whenNoEligibleKeywords() {
        // given
        val emptyResult = DefaultQueryResult<RecommendedKeyword>(
            results = emptyList(),
            requestPage = 1L,
            requestLimit = 10,
            totalResultsCount = 0L,
            hasNext = false
        )

        every { findRecommendedKeywordOutput.findAllEligibleForDisplay(1, 10) } returns emptyResult

        // when
        val result = recommendedKeywordQueryService.findForDisplay(page = 1, limit = 10)

        // then
        assertThat(result.results).isEmpty()
        assertThat(result.totalResultsCount).isEqualTo(0)
        assertThat(result.hasNext).isFalse()

        verify { findRecommendedKeywordOutput.findAllEligibleForDisplay(1, 10) }
    }

    @Test
    @DisplayName("다음 페이지가 있는 경우")
    fun findForDisplay_shouldIndicateHasNext_whenMoreResultsExist() {
        // given
        val keywords = RecommendedKeywordFixture.createEligibleForDisplayList(10)
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 1L,
            requestLimit = 10,
            totalResultsCount = 25L,
            hasNext = true
        )

        every { findRecommendedKeywordOutput.findAllEligibleForDisplay(1, 10) } returns queryResult

        // when
        val result = recommendedKeywordQueryService.findForDisplay(page = 1, limit = 10)

        // then
        assertThat(result.results).hasSize(10)
        assertThat(result.totalResultsCount).isEqualTo(25)
        assertThat(result.hasNext).isTrue()

        verify { findRecommendedKeywordOutput.findAllEligibleForDisplay(1, 10) }
    }

    @Test
    @DisplayName("두 번째 페이지 조회")
    fun findForDisplay_shouldReturnSecondPage() {
        // given
        val keywords = RecommendedKeywordFixture.createEligibleForDisplayList(3)
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 2L,
            requestLimit = 10,
            totalResultsCount = 13L,
            hasNext = false
        )

        every { findRecommendedKeywordOutput.findAllEligibleForDisplay(2, 10) } returns queryResult

        // when
        val result = recommendedKeywordQueryService.findForDisplay(page = 2, limit = 10)

        // then
        assertThat(result.results).hasSize(3)
        assertThat(result.requestPage).isEqualTo(2)
        assertThat(result.totalResultsCount).isEqualTo(13)
        assertThat(result.hasNext).isFalse()

        verify { findRecommendedKeywordOutput.findAllEligibleForDisplay(2, 10) }
    }

    @Test
    @DisplayName("랜덤 추천검색어 조회 성공")
    fun findRandomKeywords_shouldReturnRandomResults() {
        // given
        val randomKeywords = RecommendedKeywordFixture.createRandomKeywordsList(5)

        every { findRecommendedKeywordOutput.findRandomEligibleKeywords(5) } returns randomKeywords

        // when
        val results = recommendedKeywordQueryService.findRandomKeywords(5)

        // then
        assertThat(results).hasSize(5)
        // 랜덤 조회 결과는 모두 노출 가능한 키워드여야 함
        assertThat(results.all { it.isEligibleForDisplay() }).isTrue()

        verify { findRecommendedKeywordOutput.findRandomEligibleKeywords(5) }
    }

    @Test
    @DisplayName("랜덤 추천검색어 조회 - 요청 개수보다 적은 결과")
    fun findRandomKeywords_shouldReturnAvailableResults_whenLessAvailable() {
        // given - 3개만 조회 가능
        val availableKeywords = RecommendedKeywordFixture.createRandomKeywordsList(3)

        every { findRecommendedKeywordOutput.findRandomEligibleKeywords(10) } returns availableKeywords

        // when - 10개 요청
        val results = recommendedKeywordQueryService.findRandomKeywords(10)

        // then - 3개만 반환
        assertThat(results).hasSize(3)

        verify { findRecommendedKeywordOutput.findRandomEligibleKeywords(10) }
    }

    @Test
    @DisplayName("랜덤 추천검색어 조회 - 결과 없음")
    fun findRandomKeywords_shouldReturnEmptyList_whenNoEligibleKeywords() {
        // given
        every { findRecommendedKeywordOutput.findRandomEligibleKeywords(5) } returns emptyList()

        // when
        val results = recommendedKeywordQueryService.findRandomKeywords(5)

        // then
        assertThat(results).isEmpty()

        verify { findRecommendedKeywordOutput.findRandomEligibleKeywords(5) }
    }

    @Test
    @DisplayName("많은 수의 랜덤 키워드 조회")
    fun findRandomKeywords_shouldHandleLargeCount() {
        // given
        val manyKeywords = RecommendedKeywordFixture.createRandomKeywordsList(50)

        every { findRecommendedKeywordOutput.findRandomEligibleKeywords(50) } returns manyKeywords

        // when
        val results = recommendedKeywordQueryService.findRandomKeywords(50)

        // then
        assertThat(results).hasSize(50)

        verify { findRecommendedKeywordOutput.findRandomEligibleKeywords(50) }
    }

    @Test
    @DisplayName("관리자용 검색 - 전체 조건 적용")
    fun search_shouldApplyAllFilters() {
        // given
        val keywords = listOf(
            RecommendedKeywordFixture.createDomain(keyword = "피자", weight = 100, totalScore = 365),
            RecommendedKeywordFixture.createDomain(keyword = "파스타", weight = 80, totalScore = 315)
        )
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 1L,
            requestLimit = 20,
            totalResultsCount = 2L,
            hasNext = false
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = "파",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 50,
                weightLte = 150,
                totalScoreGte = 300,
                totalScoreLte = 400,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search(
            keyword = "파",
            registrationType = RegistrationType.Defined.MANUAL,
            weightGte = 50,
            weightLte = 150,
            totalScoreGte = 300,
            totalScoreLte = 400,
            page = 1,
            limit = 20
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.totalResultsCount).isEqualTo(2)
        assertThat(result.requestPage).isEqualTo(1)
        assertThat(result.requestLimit).isEqualTo(20)
        assertThat(result.hasNext).isFalse()

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = "파",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 50,
                weightLte = 150,
                totalScoreGte = 300,
                totalScoreLte = 400,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("관리자용 검색 - 자동 등록 타입 필터")
    fun search_shouldFilterByAutomaticType() {
        // given
        val automaticKeywords = listOf(
            RecommendedKeywordFixture.createDomain(keyword = "자동키워드1", isManual = false),
            RecommendedKeywordFixture.createDomain(keyword = "자동키워드2", isManual = false)
        )
        val queryResult = DefaultQueryResult(
            results = automaticKeywords,
            requestPage = 1L,
            requestLimit = 20,
            totalResultsCount = 2L,
            hasNext = false
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = RegistrationType.Defined.AUTOMATIC,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search(
            registrationType = RegistrationType.Defined.AUTOMATIC
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.all { !it.isManual }).isTrue()

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = RegistrationType.Defined.AUTOMATIC,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("관리자용 검색 - 기본값 사용")
    fun search_shouldUseDefaultValues() {
        // given
        val keywords = listOf(RecommendedKeywordFixture.createDomain())
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 1L,
            requestLimit = 20,
            totalResultsCount = 1L,
            hasNext = false
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search()

        // then
        assertThat(result.results).hasSize(1)

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("키워드 부분 검색")
    fun search_shouldSearchByKeywordContains() {
        // given
        val foundKeywords = listOf(
            RecommendedKeywordFixture.createDomain(keyword = "치킨버거"),
            RecommendedKeywordFixture.createDomain(keyword = "치킨덮밥")
        )
        val queryResult = DefaultQueryResult(
            results = foundKeywords,
            requestPage = 1L,
            requestLimit = 20,
            totalResultsCount = 2L,
            hasNext = false
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = "치킨",
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search(keyword = "치킨")

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.map { it.keyword }).allMatch { it.contains("치킨") }

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = "치킨",
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("가중치 범위로 검색")
    fun search_shouldFilterByWeightRange() {
        // given
        val keywords = listOf(
            RecommendedKeywordFixture.createDomain(keyword = "키워드1", weight = 80),
            RecommendedKeywordFixture.createDomain(keyword = "키워드2", weight = 120)
        )
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 1L,
            requestLimit = 20,
            totalResultsCount = 2L,
            hasNext = false
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = 50,
                weightLte = 150,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search(
            weightGte = 50,
            weightLte = 150
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.map { it.weight }).allMatch { it!! >= 50 && it <= 150 }

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = 50,
                weightLte = 150,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("총점 범위로 검색")
    fun search_shouldFilterByTotalScoreRange() {
        // given
        val keywords = listOf(
            RecommendedKeywordFixture.createDomain(keyword = "키워드1", totalScore = 250),
            RecommendedKeywordFixture.createDomain(keyword = "키워드2", totalScore = 350)
        )
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 1L,
            requestLimit = 20,
            totalResultsCount = 2L,
            hasNext = false
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = 200,
                totalScoreLte = 400,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search(
            totalScoreGte = 200,
            totalScoreLte = 400
        )

        // then
        assertThat(result.results).hasSize(2)
        assertThat(result.results.map { it.totalScore }).allMatch { it >= 200 && it <= 400 }

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = 200,
                totalScoreLte = 400,
                page = 1,
                limit = 20
            )
        }
    }

    @Test
    @DisplayName("페이징 처리 확인")
    fun search_shouldHandlePagination() {
        // given
        val keywords = listOf(RecommendedKeywordFixture.createDomain())
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 2L,
            requestLimit = 10,
            totalResultsCount = 15L,
            hasNext = true
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 2,
                limit = 10
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search(page = 2, limit = 10)

        // then
        assertThat(result.requestPage).isEqualTo(2)
        assertThat(result.requestLimit).isEqualTo(10)
        assertThat(result.totalResultsCount).isEqualTo(15)
        assertThat(result.hasNext).isTrue()

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = null,
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 2,
                limit = 10
            )
        }
    }

    @Test
    @DisplayName("복합 조건 검색")
    fun search_shouldApplyMultipleFilters() {
        // given
        val keywords = listOf(
            RecommendedKeywordFixture.createDomain(
                keyword = "치킨",
                weight = 100,
                totalScore = 300,
                isManual = true
            )
        )
        val queryResult = DefaultQueryResult(
            results = keywords,
            requestPage = 1L,
            requestLimit = 5,
            totalResultsCount = 1L,
            hasNext = false
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = "치",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 80,
                weightLte = 120,
                totalScoreGte = 250,
                totalScoreLte = 350,
                page = 1,
                limit = 5
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search(
            keyword = "치",
            registrationType = RegistrationType.Defined.MANUAL,
            weightGte = 80,
            weightLte = 120,
            totalScoreGte = 250,
            totalScoreLte = 350,
            page = 1,
            limit = 5
        )

        // then
        assertThat(result.results).hasSize(1)
        assertThat(result.results.first().keyword).contains("치")
        assertThat(result.results.first().isManual).isTrue()
        assertThat(result.results.first().weight).isBetween(80, 120)
        assertThat(result.results.first().totalScore).isBetween(250, 350)
        assertThat(result.requestLimit).isEqualTo(5)

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = "치",
                registrationType = RegistrationType.Defined.MANUAL,
                weightGte = 80,
                weightLte = 120,
                totalScoreGte = 250,
                totalScoreLte = 350,
                page = 1,
                limit = 5
            )
        }
    }

    @Test
    @DisplayName("검색 결과 없음")
    fun search_shouldReturnEmptyResult_whenNoMatch() {
        // given
        val queryResult = DefaultQueryResult<RecommendedKeyword>(
            results = emptyList(),
            requestPage = 1L,
            requestLimit = 20,
            totalResultsCount = 0L,
            hasNext = false
        )

        every {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = "존재하지않는키워드",
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        } returns queryResult

        // when
        val result = recommendedKeywordQueryService.search(keyword = "존재하지않는키워드")

        // then
        assertThat(result.results).isEmpty()
        assertThat(result.totalResultsCount).isEqualTo(0)
        assertThat(result.hasNext).isFalse()

        verify {
            findRecommendedKeywordOutput.findAllForSearch(
                keyword = "존재하지않는키워드",
                registrationType = null,
                weightGte = null,
                weightLte = null,
                totalScoreGte = null,
                totalScoreLte = null,
                page = 1,
                limit = 20
            )
        }
    }
}
