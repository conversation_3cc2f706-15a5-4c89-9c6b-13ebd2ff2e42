package kr.co.passorder.passordersearchservice.keyword.application.service

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.command.CreateRecommendedKeywordCommand
import kr.co.passorder.passordersearchservice.keyword.application.port.`in`.command.UpdateRecommendedKeywordWeightCommand
import kr.co.passorder.passordersearchservice.keyword.application.port.out.FindRecommendedKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.application.port.out.SaveRecommendedKeywordOutput
import kr.co.passorder.passordersearchservice.keyword.domain.exception.RecommendedKeywordNotFoundException
import kr.co.passorder.passordersearchservice.keyword.fixture.RecommendedKeywordFixture
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.UUID

@DisplayName("RecommendedKeywordService 테스트")
class RecommendedKeywordServiceTest {

    private lateinit var recommendedKeywordService: RecommendedKeywordService
    private lateinit var findRecommendedKeywordOutput: FindRecommendedKeywordOutput
    private lateinit var saveRecommendedKeywordOutput: SaveRecommendedKeywordOutput

    @BeforeEach
    fun setUp() {
        findRecommendedKeywordOutput = mockk()
        saveRecommendedKeywordOutput = mockk()

        recommendedKeywordService = RecommendedKeywordService(
            findRecommendedKeywordOutput = findRecommendedKeywordOutput,
            saveRecommendedKeywordOutput = saveRecommendedKeywordOutput
        )
    }

    @Test
    @DisplayName("추천검색어 생성 성공")
    fun create_shouldCreateRecommendedKeywordSuccessfully() {
        // given
        val command = CreateRecommendedKeywordCommand(
            keyword = "피자",
            weight = 150
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(
            keyword = "피자", 
            weight = 150, 
            isManual = true
        )

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("피자")
        assertThat(result.weight).isEqualTo(150)
        assertThat(result.isManual).isTrue()
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("추천검색어 생성 - Factory 패턴 사용 확인")
    fun create_shouldUseFactoryPattern() {
        // given
        val command = CreateRecommendedKeywordCommand(
            keyword = "햄버거",
            weight = 200
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(
            keyword = "햄버거", 
            weight = 200, 
            isManual = true
        )

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("햄버거")
        assertThat(result.weight).isEqualTo(200)
        assertThat(result.isManual).isTrue() // Factory에서 수동 등록으로 생성됨
        
        // RecommendedKeywordFactory.createManual()로 생성된 객체가 저장되는지 확인
        verify { saveRecommendedKeywordOutput.save(match { 
            it.keyword == "햄버거" && it.weight == 200 && it.isManual 
        }) }
    }

    @Test
    @DisplayName("빈 가중치로 추천검색어 생성")
    fun create_shouldCreateWithNullWeight() {
        // given
        val command = CreateRecommendedKeywordCommand(
            keyword = "자동키워드",
            weight = null // 가중치 없음
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(
            keyword = "자동키워드", 
            weight = null,
            isManual = true // Factory.createManual()는 항상 수동 등록
        )

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("자동키워드")
        assertThat(result.weight).isNull()
        assertThat(result.isManual).isTrue() // Factory 패턴에 의해 수동 등록으로 처리
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("가중치가 0인 추천검색어 생성")
    fun create_shouldCreateWithZeroWeight() {
        // given
        val command = CreateRecommendedKeywordCommand(
            keyword = "제로가중치",
            weight = 0
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(keyword = "제로가중치", weight = 0)

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("제로가중치")
        assertThat(result.weight).isEqualTo(0)
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("음수 가중치로 추천검색어 생성")
    fun create_shouldCreateWithNegativeWeight() {
        // given
        val command = CreateRecommendedKeywordCommand(
            keyword = "음수가중치",
            weight = -100
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(keyword = "음수가중치", weight = -100)

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("음수가중치")
        assertThat(result.weight).isEqualTo(-100)
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("큰 가중치 값으로 추천검색어 생성")
    fun create_shouldCreateWithLargeWeight() {
        // given
        val command = CreateRecommendedKeywordCommand(
            keyword = "큰가중치",
            weight = 999999
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(keyword = "큰가중치", weight = 999999)

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo("큰가중치")
        assertThat(result.weight).isEqualTo(999999)
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("가중치 수정 성공")
    fun updateWeight_shouldUpdateWeightSuccessfully() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdateRecommendedKeywordWeightCommand(
            identifier = keywordId,
            weight = 300
        )
        val existingKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId, 
            keyword = "피자",
            weight = 150,
            searchCount = 100,
            searchResultCount = 50
        )
        val beforeTotalScore = existingKeyword.totalScore
        
        // 가중치 업데이트 후의 키워드 (도메인 로직에 의해 totalScore 재계산됨)
        val updatedKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId,
            keyword = "피자",
            weight = 300,
            totalScore = existingKeyword.searchCount!! + existingKeyword.searchResultCount!! + 300
        )

        every { findRecommendedKeywordOutput.findById(keywordId) } returns existingKeyword
        every { saveRecommendedKeywordOutput.save(any()) } returns updatedKeyword

        // when
        val result = recommendedKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(300)
        assertThat(result.totalScore).isGreaterThan(beforeTotalScore)
        
        verify { findRecommendedKeywordOutput.findById(keywordId) }
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("존재하지 않는 키워드 가중치 수정 시 예외 발생")  
    fun updateWeight_shouldThrowNotFoundException_whenKeywordNotExists() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdateRecommendedKeywordWeightCommand(
            identifier = keywordId,
            weight = 300
        )

        every { findRecommendedKeywordOutput.findById(keywordId) } returns null

        // when & then
        assertThrows<RecommendedKeywordNotFoundException> {
            recommendedKeywordService.updateWeight(command)
        }

        verify { findRecommendedKeywordOutput.findById(keywordId) }
        verify(exactly = 0) { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("가중치 수정 후 도메인 객체 상태 변경 확인")
    fun updateWeight_shouldUpdateDomainObjectState() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdateRecommendedKeywordWeightCommand(
            identifier = keywordId,
            weight = 400
        )
        val originalKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId,
            keyword = "치킨",
            weight = 100,
            searchCount = 200,
            searchResultCount = 150
        )

        every { findRecommendedKeywordOutput.findById(keywordId) } returns originalKeyword
        every { saveRecommendedKeywordOutput.save(any()) } answers {
            val keyword = firstArg<kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword>()
            // 도메인 객체의 updateWeight 메소드가 호출된 후의 상태를 시뮬레이션
            RecommendedKeywordFixture.createDomain(
                identifier = keyword.identifier,
                keyword = keyword.keyword,
                weight = keyword.weight,
                totalScore = keyword.searchCount!! + keyword.searchResultCount!! + keyword.weight!!
            )
        }

        // when
        val result = recommendedKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(400)
        assertThat(result.totalScore).isEqualTo(200 + 150 + 400) // searchCount + searchResultCount + weight
        
        // 도메인 객체의 updateWeight 메소드가 호출되는지 확인
        verify { saveRecommendedKeywordOutput.save(match { 
            it.weight == 400 
        }) }
    }

    @Test
    @DisplayName("가중치를 다른 값으로 수정")
    fun updateWeight_shouldUpdateToDifferentValue() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdateRecommendedKeywordWeightCommand(
            identifier = keywordId,
            weight = 250
        )
        val existingKeyword = RecommendedKeywordFixture.createDomain(identifier = keywordId, weight = 200)
        val updatedKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId,
            weight = 250,
            totalScore = existingKeyword.searchCount!! + existingKeyword.searchResultCount!! + 250
        )

        every { findRecommendedKeywordOutput.findById(keywordId) } returns existingKeyword
        every { saveRecommendedKeywordOutput.save(any()) } returns updatedKeyword

        // when
        val result = recommendedKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(250)
        
        verify { findRecommendedKeywordOutput.findById(keywordId) }
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("가중치를 0으로 수정")
    fun updateWeight_shouldUpdateToZero() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdateRecommendedKeywordWeightCommand(
            identifier = keywordId,
            weight = 0
        )
        val existingKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId, 
            weight = 100,
            searchCount = 50,
            searchResultCount = 30
        )
        val updatedKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId,
            weight = 0,
            totalScore = existingKeyword.searchCount!! + existingKeyword.searchResultCount!! + 0
        )

        every { findRecommendedKeywordOutput.findById(keywordId) } returns existingKeyword
        every { saveRecommendedKeywordOutput.save(any()) } returns updatedKeyword

        // when
        val result = recommendedKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(0)
        
        verify { findRecommendedKeywordOutput.findById(keywordId) }
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("가중치를 음수로 수정")
    fun updateWeight_shouldUpdateToNegative() {
        // given
        val keywordId = UUID.randomUUID()
        val command = UpdateRecommendedKeywordWeightCommand(
            identifier = keywordId,
            weight = -50
        )
        val existingKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId, 
            weight = 100,
            searchCount = 80,
            searchResultCount = 40
        )
        val updatedKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId,
            weight = -50,
            totalScore = existingKeyword.searchCount!! + existingKeyword.searchResultCount!! + (-50)
        )

        every { findRecommendedKeywordOutput.findById(keywordId) } returns existingKeyword
        every { saveRecommendedKeywordOutput.save(any()) } returns updatedKeyword

        // when
        val result = recommendedKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(-50)
        
        verify { findRecommendedKeywordOutput.findById(keywordId) }
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("동일한 가중치로 수정")
    fun updateWeight_shouldUpdateToSameValue() {
        // given
        val keywordId = UUID.randomUUID()
        val currentWeight = 150
        val command = UpdateRecommendedKeywordWeightCommand(
            identifier = keywordId,
            weight = currentWeight
        )
        val existingKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId, 
            weight = currentWeight,
            searchCount = 60,
            searchResultCount = 40
        )
        val updatedKeyword = RecommendedKeywordFixture.createDomain(
            identifier = keywordId,
            weight = currentWeight,
            totalScore = existingKeyword.searchCount!! + existingKeyword.searchResultCount!! + currentWeight
        )

        every { findRecommendedKeywordOutput.findById(keywordId) } returns existingKeyword
        every { saveRecommendedKeywordOutput.save(any()) } returns updatedKeyword

        // when
        val result = recommendedKeywordService.updateWeight(command)

        // then
        assertThat(result.weight).isEqualTo(currentWeight)
        
        verify { findRecommendedKeywordOutput.findById(keywordId) }
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("추천검색어 생성 후 트랜잭션 처리 확인")
    fun create_shouldHandleTransactionProperly() {
        // given
        val command = CreateRecommendedKeywordCommand(
            keyword = "트랜잭션테스트",
            weight = 100
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(keyword = "트랜잭션테스트", weight = 100)

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result).isNotNull
        assertThat(result.keyword).isEqualTo("트랜잭션테스트")
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("특수문자가 포함된 키워드 생성")
    fun create_shouldCreateWithSpecialCharacters() {
        // given
        val specialKeyword = "치킨&피자!"
        val command = CreateRecommendedKeywordCommand(
            keyword = specialKeyword,
            weight = 100
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(keyword = specialKeyword, weight = 100)

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo(specialKeyword)
        assertThat(result.weight).isEqualTo(100)
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("긴 키워드 생성")
    fun create_shouldCreateWithLongKeyword() {
        // given
        val longKeyword = "매우긴키워드이름을가진음식키워드테스트용"
        val command = CreateRecommendedKeywordCommand(
            keyword = longKeyword,
            weight = 50
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(keyword = longKeyword, weight = 50)

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo(longKeyword)
        assertThat(result.weight).isEqualTo(50)
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }

    @Test
    @DisplayName("짧은 키워드 생성")
    fun create_shouldCreateWithShortKeyword() {
        // given
        val shortKeyword = "짜"
        val command = CreateRecommendedKeywordCommand(
            keyword = shortKeyword,
            weight = 75
        )
        val savedKeyword = RecommendedKeywordFixture.createDomain(keyword = shortKeyword, weight = 75)

        every { saveRecommendedKeywordOutput.save(any()) } returns savedKeyword

        // when
        val result = recommendedKeywordService.create(command)

        // then
        assertThat(result.keyword).isEqualTo(shortKeyword)
        assertThat(result.weight).isEqualTo(75)
        
        verify { saveRecommendedKeywordOutput.save(any()) }
    }
}
