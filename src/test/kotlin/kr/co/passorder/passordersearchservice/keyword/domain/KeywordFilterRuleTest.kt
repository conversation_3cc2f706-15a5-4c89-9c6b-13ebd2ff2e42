package kr.co.passorder.passordersearchservice.keyword.domain

import kr.co.passorder.passordersearchservice.keyword.domain.enums.KeywordFilterType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.util.UUID

@DisplayName("KeywordFilterRule 도메인 테스트")
class KeywordFilterRuleTest {

    @Test
    @DisplayName("키워드 필터 활성화")
    fun enable_shouldSetEnabledToTrue() {
        // given
        val filterRule = createKeywordFilterRule(isEnabled = false)
        val beforeUpdate = filterRule.updatedDate

        // when
        Thread.sleep(1) // 시간 차이를 위해
        filterRule.enable()

        // then
        assertThat(filterRule.isEnabled).isTrue
        assertThat(filterRule.updatedDate).isAfter(beforeUpdate)
    }

    @Test
    @DisplayName("키워드 필터 비활성화")
    fun disable_shouldSetEnabledToFalse() {
        // given
        val filterRule = createKeywordFilterRule(isEnabled = true)
        val beforeUpdate = filterRule.updatedDate

        // when
        Thread.sleep(1) // 시간 차이를 위해
        filterRule.disable()

        // then
        assertThat(filterRule.isEnabled).isFalse
        assertThat(filterRule.updatedDate).isAfter(beforeUpdate)
    }

    @Test
    @DisplayName("활성화된 필터 규칙 - 키워드 완전 일치 시 필터링")
    fun shouldFilter_shouldReturnTrue_whenEnabledAndKeywordExactlyMatchesTerm() {
        // given
        val filterRule = createKeywordFilterRule(
            term = "금지어",
            isEnabled = true
        )

        // when & then
        assertThat(filterRule.shouldFilter("금지어")).isTrue
        assertThat(filterRule.shouldFilter("이것은금지어입니다")).isFalse // 완전히 일치하지 않음
        assertThat(filterRule.shouldFilter("금지어가포함된문장")).isFalse // 완전히 일치하지 않음
    }

    @Test
    @DisplayName("활성화된 필터 규칙 - 키워드 불일치 시 필터링 안함")
    fun shouldFilter_shouldReturnFalse_whenEnabledButKeywordDoesNotExactlyMatchTerm() {
        // given
        val filterRule = createKeywordFilterRule(
            term = "금지어",
            isEnabled = true
        )

        // when & then
        assertThat(filterRule.shouldFilter("일반키워드")).isFalse
        assertThat(filterRule.shouldFilter("허용된문장")).isFalse
        assertThat(filterRule.shouldFilter("금지어가포함된")).isFalse // 부분 일치는 필터링 안함
        assertThat(filterRule.shouldFilter("이것은금지어")).isFalse // 부분 일치는 필터링 안함
    }

    @Test
    @DisplayName("비활성화된 필터 규칙 - 키워드 일치 시에도 필터링 안함")
    fun shouldFilter_shouldReturnFalse_whenDisabledEvenIfKeywordExactlyMatchesTerm() {
        // given
        val filterRule = createKeywordFilterRule(
            term = "금지어",
            isEnabled = false
        )

        // when & then
        assertThat(filterRule.shouldFilter("금지어")).isFalse
    }

    @Test
    @DisplayName("대소문자 구분 없이 필터링 (완전 일치)")
    fun shouldFilter_shouldBeCaseInsensitive() {
        // given
        val filterRule = createKeywordFilterRule(
            term = "Banned",
            isEnabled = true
        )

        // when & then
        assertThat(filterRule.shouldFilter("banned")).isTrue
        assertThat(filterRule.shouldFilter("BANNED")).isTrue
        assertThat(filterRule.shouldFilter("BaNnEd")).isTrue
        assertThat(filterRule.shouldFilter("이것은banned입니다")).isFalse // 부분 일치는 필터링 안함
    }

    @Test
    @DisplayName("매장 키워드 타입 확인")
    fun isStoreKeyword_shouldReturnTrue_whenStoreKeywordOrStoreSynonym() {
        // given
        val storeKeywordRule = createKeywordFilterRule(type = KeywordFilterType.Defined.STORE_KEYWORD)
        val storeSynonymRule = createKeywordFilterRule(type = KeywordFilterType.Defined.STORE_SYNONYM)
        val bannedWordRule = createKeywordFilterRule(type = KeywordFilterType.Defined.BANNED_WORD)

        // when & then
        assertThat(storeKeywordRule.isStoreKeyword()).isTrue
        assertThat(storeSynonymRule.isStoreKeyword()).isTrue
        assertThat(bannedWordRule.isStoreKeyword()).isFalse
    }

    @Test
    @DisplayName("금지어 타입 확인")
    fun isBannedWord_shouldReturnTrue_whenBannedWordType() {
        // given
        val bannedWordRule = createKeywordFilterRule(type = KeywordFilterType.Defined.BANNED_WORD)
        val storeKeywordRule = createKeywordFilterRule(type = KeywordFilterType.Defined.STORE_KEYWORD)
        val spamRule = createKeywordFilterRule(type = KeywordFilterType.Defined.SPAM)

        // when & then
        assertThat(bannedWordRule.isBannedWord()).isTrue
        assertThat(storeKeywordRule.isBannedWord()).isFalse
        assertThat(spamRule.isBannedWord()).isFalse
    }

    @Test
    @DisplayName("스팸 키워드 타입 확인")
    fun isSpamKeyword_shouldReturnTrue_whenSpamType() {
        // given
        val spamRule = createKeywordFilterRule(type = KeywordFilterType.Defined.SPAM)
        val bannedWordRule = createKeywordFilterRule(type = KeywordFilterType.Defined.BANNED_WORD)
        val storeKeywordRule = createKeywordFilterRule(type = KeywordFilterType.Defined.STORE_KEYWORD)

        // when & then
        assertThat(spamRule.isSpamKeyword()).isTrue
        assertThat(bannedWordRule.isSpamKeyword()).isFalse
        assertThat(storeKeywordRule.isSpamKeyword()).isFalse
    }

    private fun createKeywordFilterRule(
        identifier: UUID = UUID.randomUUID(),
        term: String = "테스트용어",
        type: KeywordFilterType = KeywordFilterType.Defined.BANNED_WORD,
        isEnabled: Boolean = true,
        createdDate: OffsetDateTime = OffsetDateTime.now(),
        updatedDate: OffsetDateTime = OffsetDateTime.now()
    ): KeywordFilterRule {
        return KeywordFilterRule(
            identifier = identifier,
            term = term,
            type = type,
            isEnabled = isEnabled,
            createdDate = createdDate,
            updatedDate = updatedDate
        )
    }
}
