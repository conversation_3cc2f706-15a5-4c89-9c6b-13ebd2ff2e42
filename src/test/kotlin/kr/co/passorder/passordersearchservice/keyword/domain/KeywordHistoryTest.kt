package kr.co.passorder.passordersearchservice.keyword.domain

import kr.co.passorder.passordersearchservice.keyword.domain.enums.KeywordType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.util.UUID

@DisplayName("KeywordHistory 도메인 테스트")
class KeywordHistoryTest {

    @Test
    @DisplayName("인기검색어 이력 타입 확인")
    fun isPopularKeywordHistory_shouldReturnTrue_whenPopularType() {
        // given
        val popularHistory = createKeywordHistory(type = KeywordType.Defined.POPULAR)
        val recommendedHistory = createKeywordHistory(type = KeywordType.Defined.RECOMMENDED)

        // when & then
        assertThat(popularHistory.isPopularKeywordHistory()).isTrue
        assertThat(recommendedHistory.isPopularKeywordHistory()).isFalse
    }

    @Test
    @DisplayName("추천검색어 이력 타입 확인")
    fun isRecommendedKeywordHistory_shouldReturnTrue_whenRecommendedType() {
        // given
        val recommendedHistory = createKeywordHistory(type = KeywordType.Defined.RECOMMENDED)
        val popularHistory = createKeywordHistory(type = KeywordType.Defined.POPULAR)

        // when & then
        assertThat(recommendedHistory.isRecommendedKeywordHistory()).isTrue
        assertThat(popularHistory.isRecommendedKeywordHistory()).isFalse
    }

    @Test
    @DisplayName("인기검색어 이력 - 주문 수 포함")
    fun popularKeywordHistory_shouldHaveOrderCount() {
        // given
        val popularHistory = createKeywordHistory(
            type = KeywordType.Defined.POPULAR,
            orderCount = 50
        )

        // when & then
        assertThat(popularHistory.type).isEqualTo(KeywordType.Defined.POPULAR)
        assertThat(popularHistory.orderCount).isEqualTo(50)
        assertThat(popularHistory.searchResultCount).isNull()
    }

    @Test
    @DisplayName("추천검색어 이력 - 검색 결과 수 포함")
    fun recommendedKeywordHistory_shouldHaveSearchResultCount() {
        // given
        val recommendedHistory = createKeywordHistory(
            type = KeywordType.Defined.RECOMMENDED,
            searchResultCount = 1250
        )

        // when & then
        assertThat(recommendedHistory.type).isEqualTo(KeywordType.Defined.RECOMMENDED)
        assertThat(recommendedHistory.searchResultCount).isEqualTo(1250)
        assertThat(recommendedHistory.orderCount).isNull()
    }

    @Test
    @DisplayName("키워드 이력 기본 정보 확인")
    fun keywordHistory_shouldHaveBasicInformation() {
        // given
        val keyword = "치킨"
        val rank = 3
        val totalScore = 450
        val searchCount = 100
        val weight = 50
        val recordedDate = OffsetDateTime.now().minusDays(1)

        val history = createKeywordHistory(
            keyword = keyword,
            rank = rank,
            totalScore = totalScore,
            searchCount = searchCount,
            weight = weight,
            recordedDate = recordedDate
        )

        // when & then
        assertThat(history.keyword).isEqualTo(keyword)
        assertThat(history.rank).isEqualTo(rank)
        assertThat(history.totalScore).isEqualTo(totalScore)
        assertThat(history.searchCount).isEqualTo(searchCount)
        assertThat(history.weight).isEqualTo(weight)
        assertThat(history.recordedDate).isEqualTo(recordedDate)
    }

    @Test
    @DisplayName("키워드 이력 - 랭킹이 없는 경우")
    fun keywordHistory_canHaveNullRank() {
        // given
        val history = createKeywordHistory(rank = null)

        // when & then
        assertThat(history.rank).isNull()
    }

    @Test
    @DisplayName("키워드 이력 - 가중치가 없는 경우")
    fun keywordHistory_canHaveNullWeight() {
        // given
        val history = createKeywordHistory(weight = null)

        // when & then
        assertThat(history.weight).isNull()
    }

    private fun createKeywordHistory(
        identifier: UUID = UUID.randomUUID(),
        keyword: String = "테스트키워드",
        type: KeywordType = KeywordType.Defined.POPULAR,
        rank: Int? = 1,
        totalScore: Int = 100,
        searchCount: Int = 50,
        searchResultCount: Int? = null,
        orderCount: Int? = null,
        weight: Int? = 10,
        recordedDate: OffsetDateTime = OffsetDateTime.now().minusDays(1),
        createdDate: OffsetDateTime = OffsetDateTime.now()
    ): KeywordHistory {
        return KeywordHistory(
            identifier = identifier,
            keyword = keyword,
            type = type,
            rank = rank,
            totalScore = totalScore,
            searchCount = searchCount,
            searchResultCount = searchResultCount,
            orderCount = orderCount,
            weight = weight,
            recordedDate = recordedDate,
            createdDate = createdDate
        )
    }
}
