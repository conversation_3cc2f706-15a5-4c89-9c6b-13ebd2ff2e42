package kr.co.passorder.passordersearchservice.keyword.domain

import kr.co.passorder.passordersearchservice.keyword.fixture.PopularKeywordFixture
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

@DisplayName("PopularKeyword 도메인 테스트")
class PopularKeywordTest {

    @Test
    @DisplayName("가중치 수정 시 totalScore가 재계산된다")
    fun updateWeight_shouldRecalculateTotalScore() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(
            searchCount = 100,
            orderCount = 50,
            weight = 10
        )
        val initialTotalScore = popularKeyword.totalScore

        // when
        popularKeyword.updateWeight(20)

        // then
        assertThat(popularKeyword.weight).isEqualTo(20)
        assertThat(popularKeyword.totalScore).isEqualTo(170) // 100 + 50 + 20
        assertThat(popularKeyword.totalScore).isNotEqualTo(initialTotalScore)
        assertThat(popularKeyword.updatedDate).isAfter(popularKeyword.createdDate)
    }

    @Test
    @DisplayName("검색수/주문수 수정 시 totalScore가 재계산된다")
    fun updateCounts_shouldRecalculateTotalScore() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(
            searchCount = 100,
            orderCount = 50,
            weight = 10
        )

        // when
        popularKeyword.updateCounts(searchCount = 200, orderCount = 100)

        // then
        assertThat(popularKeyword.searchCount).isEqualTo(200)
        assertThat(popularKeyword.orderCount).isEqualTo(100)
        assertThat(popularKeyword.totalScore).isEqualTo(310) // 200 + 100 + 10
    }

    @Test
    @DisplayName("순위 수정 시 updatedDate가 갱신된다")
    fun updateRank_shouldUpdateTimestamp() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain()
        val originalUpdatedDate = popularKeyword.updatedDate

        // when
        Thread.sleep(1) // 시간 차이를 위해
        popularKeyword.updateRank(3)

        // then
        assertThat(popularKeyword.rank).isEqualTo(3)
        assertThat(popularKeyword.updatedDate).isAfter(originalUpdatedDate)
    }

    @Test
    @DisplayName("삭제 처리 시 isDeleted가 true가 되고 updatedDate가 갱신된다")
    fun markAsDeleted_shouldSetDeletedTrue() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain()
        val originalUpdatedDate = popularKeyword.updatedDate

        // when
        Thread.sleep(1)
        popularKeyword.markAsDeleted()

        // then
        assertThat(popularKeyword.isDeleted).isTrue()
        assertThat(popularKeyword.updatedDate).isAfter(originalUpdatedDate)
    }

    @Test
    @DisplayName("노출 가능 조건 검증 - 삭제되지 않고 순위가 있어야 한다")
    fun isEligibleForDisplay_shouldReturnCorrectResult() {
        // given
        val eligibleKeyword = PopularKeywordFixture.createDomain(rank = 1)
        val deletedKeyword = PopularKeywordFixture.createDomain(rank = 1, isDeleted = true)
        val noRankKeyword = PopularKeywordFixture.createDomain(rank = null)

        // when & then
        assertThat(eligibleKeyword.isEligibleForDisplay()).isTrue()
        assertThat(deletedKeyword.isEligibleForDisplay()).isFalse()
        assertThat(noRankKeyword.isEligibleForDisplay()).isFalse()
    }

    @Test
    @DisplayName("totalScore 계산 - null 값들은 0으로 처리된다")
    fun recalculateTotalScore_shouldHandleNullValues() {
        // given
        val keywordWithNulls = PopularKeywordFixture.createDomain(
            searchCount = null,
            orderCount = null,
            weight = null
        )

        // when
        keywordWithNulls.updateWeight(100)

        // then
        assertThat(keywordWithNulls.totalScore).isEqualTo(100) // 0 + 0 + 100
    }

    @Test
    @DisplayName("수동 등록 키워드의 초기 totalScore는 가중치만 반영된다")
    fun manualKeyword_shouldHaveWeightOnlyInitially() {
        // given & when
        val manualKeyword = PopularKeywordFixture.createDomain(
            searchCount = null, // 수동 등록 시 초기값 없음
            orderCount = null,  // 수동 등록 시 초기값 없음
            weight = 200,
            totalScore = 200,   // 가중치만 반영
            isManual = true
        )

        // then
        assertThat(manualKeyword.isManual).isTrue()
        assertThat(manualKeyword.searchCount).isNull()
        assertThat(manualKeyword.orderCount).isNull()
        assertThat(manualKeyword.weight).isEqualTo(200)
        assertThat(manualKeyword.totalScore).isEqualTo(200)
    }

    @Test
    @DisplayName("자동 수집 키워드는 검색수와 주문수를 가진다")
    fun automaticKeyword_shouldHaveSearchAndOrderCounts() {
        // given & when
        val automaticKeyword = PopularKeywordFixture.createDomain(
            searchCount = 500,
            orderCount = 300,
            weight = null, // 자동 수집 시 가중치 없음
            totalScore = 800, // 검색수 + 주문수
            isManual = false
        )

        // then
        assertThat(automaticKeyword.isManual).isFalse()
        assertThat(automaticKeyword.searchCount).isEqualTo(500)
        assertThat(automaticKeyword.orderCount).isEqualTo(300)
        assertThat(automaticKeyword.weight).isNull()
        assertThat(automaticKeyword.totalScore).isEqualTo(800)
    }

    @Test
    @DisplayName("필터링에 의한 삭제 처리")
    fun markAsDeletedByFilter_shouldMarkAsDeleted() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(rank = 5)
        val originalUpdatedDate = popularKeyword.updatedDate

        // when
        Thread.sleep(1)
        popularKeyword.markAsDeletedByFilter()

        // then
        assertThat(popularKeyword.isDeleted).isTrue()
        assertThat(popularKeyword.rank).isNull() // 삭제 시 순위도 제거
        assertThat(popularKeyword.updatedDate).isAfter(originalUpdatedDate)
    }

    @Test
    @DisplayName("이미 삭제된 키워드에 삭제 처리 시 변경 없음")
    fun markAsDeleted_shouldNotChangeIfAlreadyDeleted() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(isDeleted = true)
        val originalUpdatedDate = popularKeyword.updatedDate

        // when
        Thread.sleep(1)
        popularKeyword.markAsDeleted()

        // then
        assertThat(popularKeyword.isDeleted).isTrue()
        assertThat(popularKeyword.updatedDate).isEqualTo(originalUpdatedDate) // 변경 없음
    }

    @Test
    @DisplayName("삭제된 키워드는 순위 업데이트되지 않음")
    fun updateRank_shouldNotUpdateIfDeleted() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(isDeleted = true, rank = null)
        val originalUpdatedDate = popularKeyword.updatedDate

        // when
        Thread.sleep(1)
        popularKeyword.updateRank(1)

        // then
        assertThat(popularKeyword.rank).isNull() // 순위 변경 없음
        assertThat(popularKeyword.updatedDate).isEqualTo(originalUpdatedDate) // 시간 변경 없음
    }

    @Test
    @DisplayName("순위를 null로 설정 가능")
    fun updateRank_shouldAllowNullRank() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(rank = 5)

        // when
        popularKeyword.updateRank(null)

        // then
        assertThat(popularKeyword.rank).isNull()
    }

    @Test
    @DisplayName("가중치를 음수로 설정 가능")
    fun updateWeight_shouldAllowNegativeWeight() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(
            searchCount = 100,
            orderCount = 50,
            weight = 10
        )

        // when
        popularKeyword.updateWeight(-20)

        // then
        assertThat(popularKeyword.weight).isEqualTo(-20)
        assertThat(popularKeyword.totalScore).isEqualTo(130) // 100 + 50 + (-20)
    }

    @Test
    @DisplayName("검색수/주문수를 null로 설정 가능")
    fun updateCounts_shouldAllowNullCounts() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(
            searchCount = 100,
            orderCount = 50,
            weight = 10
        )

        // when
        popularKeyword.updateCounts(null, null)

        // then
        assertThat(popularKeyword.searchCount).isNull()
        assertThat(popularKeyword.orderCount).isNull()
        assertThat(popularKeyword.totalScore).isEqualTo(10) // 0 + 0 + 10
    }

    @Test
    @DisplayName("삭제된 키워드도 통계는 업데이트됨")
    fun updateCounts_shouldUpdateEvenIfDeleted() {
        // given
        val popularKeyword = PopularKeywordFixture.createDomain(
            searchCount = 100,
            orderCount = 50,
            isDeleted = true
        )

        // when
        popularKeyword.updateCounts(200, 100)

        // then
        assertThat(popularKeyword.searchCount).isEqualTo(200)
        assertThat(popularKeyword.orderCount).isEqualTo(100)
        assertThat(popularKeyword.isDeleted).isTrue() // 삭제 상태는 유지
    }
}
