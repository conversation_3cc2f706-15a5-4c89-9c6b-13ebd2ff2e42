package kr.co.passorder.passordersearchservice.keyword.domain

import kr.co.passorder.passordersearchservice.keyword.fixture.RecommendedKeywordFixture
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

@DisplayName("RecommendedKeyword 도메인 테스트")
class RecommendedKeywordTest {

    @Test
    @DisplayName("가중치 수정 시 totalScore가 재계산된다")
    fun updateWeight_shouldRecalculateTotalScore() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(
            searchCount = 150,
            searchResultCount = 200,
            weight = 15
        )
        val initialTotalScore = recommendedKeyword.totalScore

        // when
        recommendedKeyword.updateWeight(30)

        // then
        assertThat(recommendedKeyword.weight).isEqualTo(30)
        assertThat(recommendedKeyword.totalScore).isEqualTo(380) // 150 + 200 + 30
        assertThat(recommendedKeyword.totalScore).isNotEqualTo(initialTotalScore)
        assertThat(recommendedKeyword.updatedDate).isAfter(recommendedKeyword.createdDate)
    }

    @Test
    @DisplayName("검색수/검색결과수 수정 시 totalScore가 재계산된다")
    fun updateCounts_shouldRecalculateTotalScore() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(
            searchCount = 150,
            searchResultCount = 200,
            weight = 15
        )

        // when
        recommendedKeyword.updateCounts(searchCount = 300, searchResultCount = 400)

        // then
        assertThat(recommendedKeyword.searchCount).isEqualTo(300)
        assertThat(recommendedKeyword.searchResultCount).isEqualTo(400)
        assertThat(recommendedKeyword.totalScore).isEqualTo(715) // 300 + 400 + 15
    }

    @Test
    @DisplayName("인기검색어 노출 상태 변경 시 updatedDate가 갱신된다")
    fun updatePopularRankStatus_shouldUpdateTimestamp() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(isPopularRank = false)
        val originalUpdatedDate = recommendedKeyword.updatedDate

        // when
        Thread.sleep(1) // 시간 차이를 위해
        recommendedKeyword.updatePopularRankStatus(true)

        // then
        assertThat(recommendedKeyword.isPopularRank).isTrue()
        assertThat(recommendedKeyword.updatedDate).isAfter(originalUpdatedDate)
    }

    @Test
    @DisplayName("삭제 처리 시 isDeleted가 true가 되고 updatedDate가 갱신된다")
    fun markAsDeleted_shouldSetDeletedTrue() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain()
        val originalUpdatedDate = recommendedKeyword.updatedDate

        // when
        Thread.sleep(1)
        recommendedKeyword.markAsDeleted()

        // then
        assertThat(recommendedKeyword.isDeleted).isTrue()
        assertThat(recommendedKeyword.updatedDate).isAfter(originalUpdatedDate)
    }

    @Test
    @DisplayName("노출 가능 조건 검증 - 삭제되지 않고 인기검색어 노출이 아니어야 한다")
    fun isEligibleForDisplay_shouldReturnCorrectResult() {
        // given
        val eligibleKeyword = RecommendedKeywordFixture.createDomain(isDeleted = false, isPopularRank = false)
        val deletedKeyword = RecommendedKeywordFixture.createDomain(isDeleted = true, isPopularRank = false)
        val popularRankKeyword = RecommendedKeywordFixture.createDomain(isDeleted = false, isPopularRank = true)

        // when & then
        assertThat(eligibleKeyword.isEligibleForDisplay()).isTrue()
        assertThat(deletedKeyword.isEligibleForDisplay()).isFalse()
        assertThat(popularRankKeyword.isEligibleForDisplay()).isFalse()
    }

    @Test
    @DisplayName("totalScore 계산 - null 값들은 0으로 처리된다")
    fun recalculateTotalScore_shouldHandleNullValues() {
        // given
        val keywordWithNulls = RecommendedKeywordFixture.createDomain(
            searchCount = null,
            searchResultCount = null,
            weight = null
        )

        // when
        keywordWithNulls.updateWeight(150)

        // then
        assertThat(keywordWithNulls.totalScore).isEqualTo(150) // 0 + 0 + 150
    }

    @Test
    @DisplayName("인기검색어 연동 상태 변경 테스트")
    fun updatePopularRankStatus_shouldWorkCorrectly() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(isPopularRank = false)

        // when - 인기검색어 Top 8에 포함됨
        recommendedKeyword.updatePopularRankStatus(true)

        // then
        assertThat(recommendedKeyword.isPopularRank).isTrue()
        assertThat(recommendedKeyword.isEligibleForDisplay()).isFalse()

        // when - 인기검색어 Top 8에서 제외됨
        recommendedKeyword.updatePopularRankStatus(false)

        // then
        assertThat(recommendedKeyword.isPopularRank).isFalse()
        assertThat(recommendedKeyword.isEligibleForDisplay()).isTrue()
    }

    @Test
    @DisplayName("수동 등록 추천검색어의 초기 totalScore는 가중치만 반영된다")
    fun manualRecommendedKeyword_shouldHaveWeightOnlyInitially() {
        // given & when
        val manualKeyword = RecommendedKeywordFixture.createDomain(
            searchCount = null, // 수동 등록 시 초기값 없음
            searchResultCount = null, // 수동 등록 시 초기값 없음
            weight = 300,
            totalScore = 300, // 가중치만 반영
            isManual = true
        )

        // then
        assertThat(manualKeyword.isManual).isTrue()
        assertThat(manualKeyword.searchCount).isNull()
        assertThat(manualKeyword.searchResultCount).isNull()
        assertThat(manualKeyword.weight).isEqualTo(300)
        assertThat(manualKeyword.totalScore).isEqualTo(300)
    }

    @Test
    @DisplayName("자동 수집 추천검색어는 검색수와 검색결과수를 가진다")
    fun automaticRecommendedKeyword_shouldHaveSearchCounts() {
        // given & when
        val automaticKeyword = RecommendedKeywordFixture.createDomain(
            searchCount = 800,
            searchResultCount = 1200,
            weight = null, // 자동 수집 시 가중치 없음
            totalScore = 2000, // 검색수 + 검색결과수
            isManual = false
        )

        // then
        assertThat(automaticKeyword.isManual).isFalse()
        assertThat(automaticKeyword.searchCount).isEqualTo(800)
        assertThat(automaticKeyword.searchResultCount).isEqualTo(1200)
        assertThat(automaticKeyword.weight).isNull()
        assertThat(automaticKeyword.totalScore).isEqualTo(2000)
    }

    @Test
    @DisplayName("랜덤 노출 자격 확인 - 검색결과수가 50개 이상이어야 한다")
    fun isEligibleForRandomDisplay_shouldCheckSearchResultCount() {
        // given
        val eligibleForRandom = RecommendedKeywordFixture.createDomain(
            searchResultCount = 50,
            isDeleted = false,
            isPopularRank = false
        )
        val notEligibleForRandom = RecommendedKeywordFixture.createDomain(
            searchResultCount = 49,
            isDeleted = false,
            isPopularRank = false
        )

        // when & then
        // 실제 isEligibleForRandomDisplay 메서드가 있다면 테스트
        // 현재는 일반적인 노출 조건만 확인
        assertThat(eligibleForRandom.isEligibleForDisplay()).isTrue()
        assertThat(notEligibleForRandom.isEligibleForDisplay()).isTrue()
        
        // 검색결과수 조건 확인
        assertThat(eligibleForRandom.searchResultCount!! >= 50).isTrue()
        assertThat(notEligibleForRandom.searchResultCount!! >= 50).isFalse()
    }

    @Test
    @DisplayName("필터링에 의한 삭제 처리")
    fun markAsDeletedByFilter_shouldMarkAsDeleted() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain()
        val originalUpdatedDate = recommendedKeyword.updatedDate

        // when
        Thread.sleep(1)
        recommendedKeyword.markAsDeletedByFilter()

        // then
        assertThat(recommendedKeyword.isDeleted).isTrue()
        assertThat(recommendedKeyword.updatedDate).isAfter(originalUpdatedDate)
    }

    @Test
    @DisplayName("이미 삭제된 키워드에 삭제 처리 시 변경 없음")
    fun markAsDeleted_shouldNotChangeIfAlreadyDeleted() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(isDeleted = true)
        val originalUpdatedDate = recommendedKeyword.updatedDate

        // when
        Thread.sleep(1)
        recommendedKeyword.markAsDeleted()

        // then
        assertThat(recommendedKeyword.isDeleted).isTrue()
        assertThat(recommendedKeyword.updatedDate).isEqualTo(originalUpdatedDate) // 변경 없음
    }

    @Test
    @DisplayName("삭제된 키워드는 인기검색어 상태 업데이트되지 않음")
    fun updatePopularRankStatus_shouldNotUpdateIfDeleted() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(isDeleted = true, isPopularRank = false)
        val originalUpdatedDate = recommendedKeyword.updatedDate

        // when
        Thread.sleep(1)
        recommendedKeyword.updatePopularRankStatus(true)

        // then
        assertThat(recommendedKeyword.isPopularRank).isFalse() // 상태 변경 없음
        assertThat(recommendedKeyword.updatedDate).isEqualTo(originalUpdatedDate) // 시간 변경 없음
    }

    @Test
    @DisplayName("가중치를 음수로 설정 가능")
    fun updateWeight_shouldAllowNegativeWeight() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(
            searchCount = 100,
            searchResultCount = 150,
            weight = 20
        )

        // when
        recommendedKeyword.updateWeight(-30)

        // then
        assertThat(recommendedKeyword.weight).isEqualTo(-30)
        assertThat(recommendedKeyword.totalScore).isEqualTo(220) // 100 + 150 + (-30)
    }

    @Test
    @DisplayName("검색수/검색결과수를 null로 설정 가능")
    fun updateCounts_shouldAllowNullCounts() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(
            searchCount = 100,
            searchResultCount = 150,
            weight = 20
        )

        // when
        recommendedKeyword.updateCounts(null, null)

        // then
        assertThat(recommendedKeyword.searchCount).isNull()
        assertThat(recommendedKeyword.searchResultCount).isNull()
        assertThat(recommendedKeyword.totalScore).isEqualTo(20) // 0 + 0 + 20
    }

    @Test
    @DisplayName("삭제된 키워드도 통계는 업데이트됨")
    fun updateCounts_shouldUpdateEvenIfDeleted() {
        // given
        val recommendedKeyword = RecommendedKeywordFixture.createDomain(
            searchCount = 100,
            searchResultCount = 150,
            isDeleted = true
        )

        // when
        recommendedKeyword.updateCounts(300, 400)

        // then
        assertThat(recommendedKeyword.searchCount).isEqualTo(300)
        assertThat(recommendedKeyword.searchResultCount).isEqualTo(400)
        assertThat(recommendedKeyword.isDeleted).isTrue() // 삭제 상태는 유지
    }

    @Test
    @DisplayName("인기검색어 연동 상태와 노출 가능성 반비례 관계")
    fun isPopularRank_shouldBeInverseOfDisplayEligibility() {
        // given
        val keyword = RecommendedKeywordFixture.createDomain(isDeleted = false)

        // when & then - 인기검색어 연동 false일 때 노출 가능
        keyword.updatePopularRankStatus(false)
        assertThat(keyword.isPopularRank).isFalse()
        assertThat(keyword.isEligibleForDisplay()).isTrue()

        // when & then - 인기검색어 연동 true일 때 노출 불가
        keyword.updatePopularRankStatus(true)
        assertThat(keyword.isPopularRank).isTrue()
        assertThat(keyword.isEligibleForDisplay()).isFalse()
    }

    @Test
    @DisplayName("복합 조건 테스트 - 삭제되고 인기검색어 연동인 경우")
    fun complexCondition_shouldNotBeEligibleForDisplay() {
        // given
        val keyword = RecommendedKeywordFixture.createDomain(
            isDeleted = true,
            isPopularRank = true
        )

        // when & then
        assertThat(keyword.isEligibleForDisplay()).isFalse() // 두 조건 모두 false이므로 노출 불가
    }
}
