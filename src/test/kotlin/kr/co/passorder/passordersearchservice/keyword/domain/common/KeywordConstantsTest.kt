package kr.co.passorder.passordersearchservice.keyword.domain.common

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

@DisplayName("KeywordConstants 테스트")
class KeywordConstantsTest {

    @Test
    @DisplayName("인기검색어 상위 랭킹 사이즈 상수 확인")
    fun shouldHaveCorrectPopularTopRankSize() {
        // when & then
        assertThat(KeywordConstants.POPULAR_TOP_RANK_SIZE).isEqualTo(8)
    }

    @Test
    @DisplayName("상수 값들이 양수인지 확인")
    fun constantsShouldBePositive() {
        // when & then
        assertThat(KeywordConstants.POPULAR_TOP_RANK_SIZE).isGreaterThan(0)
    }

    @Test
    @DisplayName("인기검색어 랜킹 사이즈가 적절한 범위인지 확인")
    fun popularTopRankSize_shouldBeInReasonableRange() {
        // when & then
        assertThat(KeywordConstants.POPULAR_TOP_RANK_SIZE)
            .isGreaterThanOrEqualTo(5) // 최소 5개 이상
            .isLessThanOrEqualTo(20)   // 최대 20개 이하
    }

    @Test
    @DisplayName("상수 값이 비즈니스 요구사항과 일치하는지 확인")
    fun popularTopRankSize_shouldMatchBusinessRequirement() {
        // 비즈니스 요구사항: 인기검색어는 Top 8을 노출
        // when & then
        assertThat(KeywordConstants.POPULAR_TOP_RANK_SIZE).isEqualTo(8)
    }
}
