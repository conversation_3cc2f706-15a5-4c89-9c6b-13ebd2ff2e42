package kr.co.passorder.passordersearchservice.keyword.fixture

import kr.co.passorder.passordersearchservice.keyword.adapter.out.persistence.PopularKeywordEntity
import kr.co.passorder.passordersearchservice.keyword.domain.PopularKeyword
import java.time.OffsetDateTime
import java.util.UUID

object PopularKeywordFixture {

    fun createDomain(
        identifier: UUID = UUID.randomUUID(),
        keyword: String = "테스트키워드",
        searchCount: Int? = 100,
        orderCount: Int? = 50,
        weight: Int? = 10,
        totalScore: Int = (searchCount ?: 0) + (orderCount ?: 0) + (weight ?: 0),
        rank: Int? = null,
        isManual: Boolean = true,
        isDeleted: Boolean = false,
        createdDate: OffsetDateTime = OffsetDateTime.now(),
        updatedDate: OffsetDateTime = OffsetDateTime.now()
    ): PopularKeyword {
        return PopularKeyword(
            identifier = identifier,
            keyword = keyword,
            searchCount = searchCount,
            orderCount = orderCount,
            weight = weight,
            totalScore = totalScore,
            rank = rank,
            isManual = isManual,
            createdDate = createdDate,
            updatedDate = updatedDate,
            isDeleted = isDeleted
        )
    }

    fun createEntity(
        identifier: UUID = UUID.randomUUID(),
        keyword: String = "테스트키워드",
        searchCount: Int? = 100,
        orderCount: Int? = 50,
        weight: Int? = 10,
        totalScore: Int = (searchCount ?: 0) + (orderCount ?: 0) + (weight ?: 0),
        rank: Int? = null,
        isManual: Boolean = true,
        isDeleted: Boolean = false,
        createdDate: OffsetDateTime = OffsetDateTime.now(),
        updatedDate: OffsetDateTime = OffsetDateTime.now()
    ): PopularKeywordEntity {
        return PopularKeywordEntity(
            identifier = identifier,
            keyword = keyword,
            searchCount = searchCount,
            orderCount = orderCount,
            weight = weight,
            totalScore = totalScore,
            rank = rank,
            isManual = isManual,
            createdDate = createdDate,
            updatedDate = updatedDate,
            isDeleted = isDeleted
        )
    }

    fun createRankedList(count: Int = 8): List<PopularKeyword> {
        return (1..count).map { index ->
            createDomain(
                keyword = "키워드$index",
                rank = index,
                totalScore = 1000 - (index * 100)
            )
        }
    }

    fun createEntityRankedList(count: Int = 8): List<PopularKeywordEntity> {
        return (1..count).map { index ->
            createEntity(
                keyword = "키워드$index",
                rank = index,
                totalScore = 1000 - (index * 100)
            )
        }
    }

    fun createDomainWithKeywords(keywords: List<String>): List<PopularKeyword> {
        return keywords.mapIndexed { index, keyword ->
            createDomain(
                keyword = keyword,
                rank = index + 1,
                totalScore = 1000 - (index * 100)
            )
        }
    }

    fun createEntityWithKeywords(keywords: List<String>): List<PopularKeywordEntity> {
        return keywords.mapIndexed { index, keyword ->
            createEntity(
                keyword = keyword,
                rank = index + 1,
                totalScore = 1000 - (index * 100)
            )
        }
    }
}
