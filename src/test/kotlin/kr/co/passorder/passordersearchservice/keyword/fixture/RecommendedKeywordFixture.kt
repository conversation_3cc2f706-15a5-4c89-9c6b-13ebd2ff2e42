package kr.co.passorder.passordersearchservice.keyword.fixture

import kr.co.passorder.passordersearchservice.keyword.adapter.out.persistence.RecommendedKeywordEntity
import kr.co.passorder.passordersearchservice.keyword.domain.RecommendedKeyword
import java.time.OffsetDateTime
import java.util.UUID

object RecommendedKeywordFixture {

    fun createDomain(
        identifier: UUID = UUID.randomUUID(),
        keyword: String = "테스트추천키워드",
        searchCount: Int? = 150,
        searchResultCount: Int? = 200,
        weight: Int? = 15,
        totalScore: Int = (searchCount ?: 0) + (searchResultCount ?: 0) + (weight ?: 0),
        isManual: Boolean = false,
        isPopularRank: Boolean = false,
        isDeleted: Boolean = false,
        createdDate: OffsetDateTime = OffsetDateTime.now(),
        updatedDate: OffsetDateTime = OffsetDateTime.now()
    ): RecommendedKeyword {
        return RecommendedKeyword(
            identifier = identifier,
            keyword = keyword,
            searchCount = searchCount,
            searchResultCount = searchResultCount,
            weight = weight,
            totalScore = totalScore,
            isManual = isManual,
            isPopularRank = isPopularRank,
            createdDate = createdDate,
            updatedDate = updatedDate,
            isDeleted = isDeleted
        )
    }

    fun createEntity(
        identifier: UUID = UUID.randomUUID(),
        keyword: String = "테스트추천키워드",
        searchCount: Int? = 150,
        searchResultCount: Int? = 200,
        weight: Int? = 15,
        totalScore: Int = (searchCount ?: 0) + (searchResultCount ?: 0) + (weight ?: 0),
        isManual: Boolean = false,
        isPopularRank: Boolean = false,  
        isDeleted: Boolean = false,
        createdDate: OffsetDateTime = OffsetDateTime.now(),
        updatedDate: OffsetDateTime = OffsetDateTime.now()
    ): RecommendedKeywordEntity {
        return RecommendedKeywordEntity(
            identifier = identifier,
            keyword = keyword,
            searchCount = searchCount,
            searchResultCount = searchResultCount,
            weight = weight,
            totalScore = totalScore,
            isManual = isManual,
            isPopularRank = isPopularRank,
            createdDate = createdDate,
            updatedDate = updatedDate,
            isDeleted = isDeleted
        )
    }

    fun createEligibleForDisplayList(count: Int = 10): List<RecommendedKeyword> {
        return (1..count).map { index ->
            createDomain(
                keyword = "추천키워드$index",
                isPopularRank = false,
                isDeleted = false,
                totalScore = 500 - (index * 10)
            )
        }
    }

    fun createEntityEligibleForDisplayList(count: Int = 10): List<RecommendedKeywordEntity> {
        return (1..count).map { index ->
            createEntity(
                keyword = "추천키워드$index",
                isPopularRank = false,
                isDeleted = false,
                totalScore = 500 - (index * 10)
            )
        }
    }

    fun createDomainWithKeywords(keywords: List<String>): List<RecommendedKeyword> {
        return keywords.mapIndexed { index, keyword ->
            createDomain(
                keyword = keyword,
                totalScore = 500 - (index * 10)
            )
        }
    }

    fun createEntityWithKeywords(keywords: List<String>): List<RecommendedKeywordEntity> {
        return keywords.mapIndexed { index, keyword ->
            createEntity(
                keyword = keyword,
                totalScore = 500 - (index * 10)
            )
        }
    }

    fun createRandomKeywordsList(count: Int = 5): List<RecommendedKeyword> {
        return (1..count).map { index ->
            createDomain(
                keyword = "랜덤키워드$index",
                searchResultCount = 50 + index,
                isPopularRank = false,
                isDeleted = false
            )
        }
    }
}
